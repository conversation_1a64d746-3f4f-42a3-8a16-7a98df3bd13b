using System;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Robot3DSimulation.Models;
using Robot3DSimulation.Services;
using Robot3DSimulation.Views;

namespace Robot3DSimulation.ViewModels
{
    /// <summary>
    /// 主窗口视图模型
    /// </summary>
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly ILogger<MainWindowViewModel> _logger;
        private readonly IRobotService _robotService;
        private readonly IConfigurationService _configService;

        /// <summary>
        /// 连接状态
        /// </summary>
        [ObservableProperty]
        private string connectionStatus = "未连接";

        /// <summary>
        /// 机器人状态
        /// </summary>
        [ObservableProperty]
        private string robotStatus = "就绪";

        /// <summary>
        /// 是否已连接
        /// </summary>
        [ObservableProperty]
        private bool isConnected;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        [ObservableProperty]
        private bool isRunning;

        /// <summary>
        /// 当前T轴位置
        /// </summary>
        [ObservableProperty]
        private double currentTPosition;

        /// <summary>
        /// 当前R轴位置
        /// </summary>
        [ObservableProperty]
        private double currentRPosition;

        /// <summary>
        /// 当前Z轴位置
        /// </summary>
        [ObservableProperty]
        private double currentZPosition;

        /// <summary>
        /// 当前时间
        /// </summary>
        [ObservableProperty]
        private DateTime currentTime = DateTime.Now;

        /// <summary>
        /// 是否可以连接
        /// </summary>
        [ObservableProperty]
        private bool canConnect = true;

        /// <summary>
        /// 是否可以启动
        /// </summary>
        [ObservableProperty]
        private bool canStart;

        /// <summary>
        /// 显示3D视图
        /// </summary>
        [ObservableProperty]
        private bool show3DView = true;

        /// <summary>
        /// 显示控制面板
        /// </summary>
        [ObservableProperty]
        private bool showControlPanel = true;

        /// <summary>
        /// 显示调试面板
        /// </summary>
        [ObservableProperty]
        private bool showDebugPanel = true;

        /// <summary>
        /// 显示轨迹
        /// </summary>
        [ObservableProperty]
        private bool showTrajectory;

        /// <summary>
        /// 显示坐标轴
        /// </summary>
        [ObservableProperty]
        private bool showAxes = true;

        /// <summary>
        /// 显示网格
        /// </summary>
        [ObservableProperty]
        private bool showGrid = true;



        /// <summary>
        /// 3D视图
        /// </summary>
        [ObservableProperty]
        private Robot3DView? robot3DView;

        /// <summary>
        /// 控制面板
        /// </summary>
        [ObservableProperty]
        private RobotControlPanel? controlPanel;

        /// <summary>
        /// 调试面板
        /// </summary>
        [ObservableProperty]
        private DebugPanel? debugPanel;



        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="robotService">机器人服务</param>
        /// <param name="configService">配置服务</param>
        /// <param name="robot3DView">3D视图</param>
        /// <param name="controlPanel">控制面板</param>
        /// <param name="debugPanel">调试面板</param>
        public MainWindowViewModel(
            ILogger<MainWindowViewModel> logger,
            IRobotService robotService,
            IConfigurationService configService,
            Robot3DView robot3DView,
            RobotControlPanel controlPanel,
            DebugPanel debugPanel)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _robotService = robotService ?? throw new ArgumentNullException(nameof(robotService));
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));

            Robot3DView = robot3DView;
            ControlPanel = controlPanel;
            DebugPanel = debugPanel;

            // 订阅机器人服务事件
            _robotService.ConnectionStatusChanged += OnConnectionStatusChanged;
            _robotService.StatusChanged += OnRobotStatusChanged;
            _robotService.PositionChanged += OnPositionChanged;

            // 启动时间更新定时器
            StartTimeUpdateTimer();

            _logger.LogInformation("主窗口视图模型已初始化");
        }

        /// <summary>
        /// 连接命令
        /// </summary>
        [RelayCommand]
        private async Task ConnectAsync()
        {
            try
            {
                ConnectionStatus = "正在连接...";
                var success = await _robotService.ConnectAsync();
                
                if (success)
                {
                    ConnectionStatus = "已连接";
                    _logger.LogInformation("机器人连接成功");
                }
                else
                {
                    ConnectionStatus = "连接失败";
                    MessageBox.Show("连接机器人失败，请检查配置和网络连接。", "连接错误", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                ConnectionStatus = "连接错误";
                _logger.LogError(ex, "连接机器人时发生错误");
                MessageBox.Show($"连接时发生错误: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 断开连接命令
        /// </summary>
        [RelayCommand]
        private async Task DisconnectAsync()
        {
            try
            {
                await _robotService.DisconnectAsync();
                ConnectionStatus = "已断开";
                _logger.LogInformation("机器人连接已断开");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开连接时发生错误");
                MessageBox.Show($"断开连接时发生错误: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 启动命令
        /// </summary>
        [RelayCommand]
        private async Task StartAsync()
        {
            try
            {
                await _robotService.StartDataUpdateAsync();
                _logger.LogInformation("数据更新已启动");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动数据更新时发生错误");
                MessageBox.Show($"启动时发生错误: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 停止命令
        /// </summary>
        [RelayCommand]
        private async Task StopAsync()
        {
            try
            {
                await _robotService.StopDataUpdateAsync();
                _logger.LogInformation("数据更新已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止数据更新时发生错误");
                MessageBox.Show($"停止时发生错误: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 紧急停止命令
        /// </summary>
        [RelayCommand]
        private async Task EmergencyStopAsync()
        {
            try
            {
                await _robotService.SetEmergencyStopAsync(true);
                _logger.LogWarning("紧急停止已激活");
                MessageBox.Show("紧急停止已激活！", "紧急停止", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行紧急停止时发生错误");
                MessageBox.Show($"紧急停止时发生错误: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 复位命令
        /// </summary>
        [RelayCommand]
        private async Task ResetAsync()
        {
            try
            {
                var result = MessageBox.Show("确定要复位机器人吗？", "确认复位", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    await _robotService.ResetAsync();
                    _logger.LogInformation("机器人已复位");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复位机器人时发生错误");
                MessageBox.Show($"复位时发生错误: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 回到原点命令
        /// </summary>
        [RelayCommand]
        private async Task HomeAsync()
        {
            try
            {
                var result = MessageBox.Show("确定要回到原点吗？", "确认归零",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await _robotService.HomeAsync();
                    _logger.LogInformation("机器人正在回到原点");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "回到原点时发生错误");
                MessageBox.Show($"归零时发生错误: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// 连接状态变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="connected">连接状态</param>
        private void OnConnectionStatusChanged(object? sender, bool connected)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsConnected = connected;
                ConnectionStatus = connected ? "已连接" : "未连接";

                // 更新命令可执行状态
                ConnectCommand.NotifyCanExecuteChanged();
                StartCommand.NotifyCanExecuteChanged();
            });
        }

        /// <summary>
        /// 机器人状态变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">状态变化事件参数</param>
        private void OnRobotStatusChanged(object? sender, RobotStatusChangedEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                RobotStatus = e.StatusMessage;
                IsRunning = _robotService.IsRunning;

                // 更新命令可执行状态
                ConnectCommand.NotifyCanExecuteChanged();
                StartCommand.NotifyCanExecuteChanged();
            });
        }

        /// <summary>
        /// 位置变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">位置变化事件参数</param>
        private void OnPositionChanged(object? sender, RobotPositionChangedEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                switch (e.AxisName)
                {
                    case "T":
                        CurrentTPosition = e.NewPosition;
                        break;
                    case "R":
                        CurrentRPosition = e.NewPosition;
                        break;
                    case "Z":
                        CurrentZPosition = e.NewPosition;
                        break;
                }
            });
        }

        /// <summary>
        /// 启动时间更新定时器
        /// </summary>
        private void StartTimeUpdateTimer()
        {
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += (s, e) => CurrentTime = DateTime.Now;
            timer.Start();
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        /// <returns>清理任务</returns>
        public async Task CleanupAsync()
        {
            try
            {
                if (_robotService.IsRunning)
                {
                    await _robotService.StopDataUpdateAsync();
                }

                if (_robotService.IsConnected)
                {
                    await _robotService.DisconnectAsync();
                }

                _logger.LogInformation("主窗口资源清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理资源时发生错误");
            }
        }

        /// <summary>
        /// 退出应用程序
        /// </summary>
        [RelayCommand]
        private async void Exit()
        {
            try
            {
                await CleanupAsync();
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "退出应用程序时发生错误");
                Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// 截图命令
        /// </summary>
        [RelayCommand]
        private void Screenshot()
        {
            try
            {
                MessageBox.Show("截图功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "截图时发生错误");
                MessageBox.Show($"截图失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 关于对话框
        /// </summary>
        [RelayCommand]
        private void About()
        {
            var aboutMessage = "Robot三轴虚拟仿真调试系统\n\n" +
                              "版本: 1.0.0\n" +
                              "基于WPF和HelixToolkit开发\n" +
                              "支持Modbus通信和3D可视化\n\n" +
                              "© 2024 Robot3D仿真系统";

            MessageBox.Show(aboutMessage, "关于Robot3D仿真系统",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // 其他命令的占位符实现
        [RelayCommand] private void NewConfig() => MessageBox.Show("新建配置功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void OpenConfig() => MessageBox.Show("打开配置功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void SaveConfig() => MessageBox.Show("保存配置功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void ExportData() => MessageBox.Show("导出数据功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void ImportData() => MessageBox.Show("导入数据功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void ConnectionSettings() => MessageBox.Show("连接设置功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void TestConnection() => MessageBox.Show("测试连接功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void ConfigWizard() => MessageBox.Show("配置向导功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void Diagnostics() => MessageBox.Show("系统诊断功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void PerformanceMonitor() => MessageBox.Show("性能监控功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void RecordVideo() => MessageBox.Show("录制视频功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void UserManual() => MessageBox.Show("用户手册功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void Shortcuts() => MessageBox.Show("快捷键功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        [RelayCommand] private void CheckUpdate() => MessageBox.Show("检查更新功能待实现", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
    }
}
