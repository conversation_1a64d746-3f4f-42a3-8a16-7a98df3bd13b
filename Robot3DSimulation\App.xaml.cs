using System;
using System.IO;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Robot3DSimulation.Services;
using Robot3DSimulation.ViewModels;
using Robot3DSimulation.Views;
using Robot3DSimulation.Models;

namespace Robot3DSimulation
{
    /// <summary>
    /// Robot3D仿真应用程序主类
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        /// <summary>
        /// 应用程序启动时的初始化
        /// </summary>
        /// <param name="e">启动事件参数</param>
        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 设置全局异常处理
                SetupGlobalExceptionHandling();

                // 确保日志目录存在
                EnsureLogDirectoryExists();

                // 构建主机和依赖注入容器
                _host = CreateHostBuilder(e.Args).Build();

                // 启动主机
                await _host.StartAsync();

                // 获取日志记录器
                var logger = _host.Services.GetRequiredService<ILogger<App>>();
                logger.LogInformation("Robot3D仿真系统启动成功");

                // 获取主窗口并显示
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                var errorMessage = $"应用程序启动失败: {ex.Message}\n\n详细信息: {ex}";
                MessageBox.Show(errorMessage, "Robot3D仿真系统 - 启动错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        /// <summary>
        /// 应用程序退出时的清理
        /// </summary>
        /// <param name="e">退出事件参数</param>
        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                if (_host != null)
                {
                    var logger = _host.Services.GetService<ILogger<App>>();
                    logger?.LogInformation("Robot3D仿真系统正在关闭...");

                    await _host.StopAsync(TimeSpan.FromSeconds(5));
                    _host.Dispose();

                    logger?.LogInformation("Robot3D仿真系统已安全关闭");
                }
            }
            catch (Exception ex)
            {
                // 记录关闭时的错误，但不阻止应用程序退出
                System.Diagnostics.Debug.WriteLine($"应用程序关闭时发生错误: {ex.Message}");
            }

            base.OnExit(e);
        }

        /// <summary>
        /// 创建主机构建器并配置服务
        /// </summary>
        /// <param name="args">命令行参数</param>
        /// <returns>主机构建器</returns>
        private static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((context, config) =>
                {
                    // 配置文件设置
                    config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                })
                .ConfigureLogging((context, logging) =>
                {
                    // 日志配置
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.AddDebug();
                    logging.SetMinimumLevel(LogLevel.Information);
                })
                .ConfigureServices((context, services) =>
                {
                    // 注册配置
                    services.Configure<RobotConfiguration>(
                        context.Configuration.GetSection("Robot"));
                    services.Configure<ModbusConfiguration>(
                        context.Configuration.GetSection("Modbus"));

                    // 注册服务
                    services.AddSingleton<IModbusService, ModbusService>();
                    services.AddSingleton<IRobotService, RobotService>();
                    services.AddSingleton<I3DRenderService, Robot3DRenderService>();
                    services.AddSingleton<IConfigurationService, ConfigurationService>();

                    // 注册ViewModels
                    services.AddTransient<MainWindowViewModel>();
                    services.AddTransient<Robot3DViewModel>();
                    services.AddTransient<RobotControlPanelViewModel>();
                    services.AddTransient<DebugPanelViewModel>();

                    // 注册Views
                    services.AddTransient<MainWindow>();
                    services.AddTransient<Robot3DView>();
                    services.AddTransient<RobotControlPanel>();
                    services.AddTransient<DebugPanel>();
                });

        /// <summary>
        /// 设置全局异常处理
        /// </summary>
        private void SetupGlobalExceptionHandling()
        {
            // 处理UI线程未捕获的异常
            DispatcherUnhandledException += (sender, e) =>
            {
                var errorMessage = $"UI线程发生未处理异常:\n{e.Exception.Message}\n\n详细信息:\n{e.Exception}";
                MessageBox.Show(errorMessage, "Robot3D仿真系统 - 系统错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // 记录异常但不终止应用程序
                e.Handled = true;
            };

            // 处理非UI线程未捕获的异常
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                var exception = e.ExceptionObject as Exception;
                var errorMessage = $"应用程序域发生未处理异常:\n{exception?.Message}\n\n详细信息:\n{exception}";

                MessageBox.Show(errorMessage, "Robot3D仿真系统 - 严重错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            };
        }

        /// <summary>
        /// 确保日志目录存在
        /// </summary>
        private static void EnsureLogDirectoryExists()
        {
            try
            {
                var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建日志目录失败: {ex.Message}");
            }
        }
    }
}
