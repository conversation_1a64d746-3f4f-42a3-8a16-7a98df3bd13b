# C# WPF 计算器

一个使用 C# 9.0、WPF 和 MVVM 架构构建的现代化计算器应用程序。

## 功能特性

### 基本功能
- ✅ 基本算术运算（加法、减法、乘法、除法）
- ✅ 小数运算支持
- ✅ 连续计算功能
- ✅ 清除和退格功能
- ✅ 错误处理和显示

### 技术特性
- ✅ 使用 C# 9.0 现代特性（switch 表达式、可空引用类型）
- ✅ MVVM 架构模式
- ✅ CommunityToolkit.Mvvm 框架和 Source Generators
- ✅ 依赖注入 (Microsoft.Extensions.DependencyInjection)
- ✅ 完整的单元测试覆盖
- ✅ 现代化的 WPF 界面设计

## 项目结构

```
Calculator/
├── App.xaml                    # 应用程序定义
├── App.xaml.cs                 # 应用程序入口点和依赖注入配置
├── Calculator.csproj           # 项目文件
├── Enums/
│   └── OperationType.cs        # 操作类型枚举
├── Exceptions/
│   └── CalculatorException.cs  # 自定义异常类
├── Models/
│   └── CalculatorModel.cs      # 计算器状态模型
├── Services/
│   ├── ICalculatorService.cs   # 计算服务接口
│   └── CalculatorService.cs    # 计算服务实现
├── ViewModels/
│   └── CalculatorViewModel.cs  # 计算器视图模型
├── Views/
│   ├── MainWindow.xaml         # 主窗口界面
│   └── MainWindow.xaml.cs      # 主窗口代码
└── Tests/
    └── CalculatorServiceTests.cs # 单元测试
```

## 技术栈

- **框架**: .NET 8.0 (Windows)
- **语言**: C# 9.0
- **UI**: WPF (Windows Presentation Foundation)
- **架构**: MVVM (Model-View-ViewModel)
- **MVVM 框架**: CommunityToolkit.Mvvm
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **测试框架**: xUnit
- **模拟框架**: Moq

## 运行要求

- .NET 8.0 SDK
- Windows 操作系统
- Visual Studio 2022 或 Visual Studio Code (可选)

## 如何运行

1. 克隆或下载项目
2. 在项目根目录打开命令行
3. 运行以下命令：

```bash
# 还原依赖包
dotnet restore

# 编译项目
dotnet build

# 运行应用程序
dotnet run

# 运行测试
dotnet test
```

## 使用说明

### 基本操作
1. 点击数字按钮输入数字
2. 点击运算符按钮（+、-、×、÷）选择操作
3. 点击等号（=）执行计算
4. 点击 C 清除所有内容
5. 点击退格（⌫）删除最后一个字符

### 连续计算
- 可以在一个计算结果的基础上继续进行运算
- 支持链式计算（如：2 + 3 × 4 = 20）

### 错误处理
- 除零操作会显示错误消息
- 无效输入会被自动处理
- 错误状态下可以通过清除或新输入恢复

## 架构设计

### MVVM 模式
- **Model**: `CalculatorModel` - 管理计算器状态
- **View**: `MainWindow.xaml` - 用户界面
- **ViewModel**: `CalculatorViewModel` - 界面逻辑和命令处理

### 依赖注入
应用程序使用依赖注入容器管理服务生命周期：
- `ICalculatorService` - 计算服务
- `CalculatorViewModel` - 视图模型
- `MainWindow` - 主窗口

### C# 9.0 特性应用
- **Switch 表达式**: 用于操作类型判断和计算逻辑
- **可空引用类型**: 提高代码安全性
- **Source Generators**: CommunityToolkit.Mvvm 自动生成属性和命令

## 测试覆盖

项目包含 38 个单元测试，覆盖：
- 基本算术运算正确性
- 异常处理（除零、无效操作）
- 数字解析和验证
- 边界值处理
- 操作有效性验证

## 开发者信息

本项目按照现代 C# 和 WPF 开发最佳实践构建，展示了：
- 清晰的代码架构和分离关注点
- 完整的错误处理机制
- 全面的单元测试覆盖
- 现代化的用户界面设计
- 响应式布局和用户体验优化

## 许可证

本项目仅用于学习和演示目的。
