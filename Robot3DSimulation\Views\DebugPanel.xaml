<UserControl x:Class="Robot3DSimulation.Views.DebugPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="300">
    
    <UserControl.Resources>
        <!-- 调试面板样式 -->
        <Style x:Key="DebugGroupStyle" TargetType="GroupBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="LogTextBoxStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
            <Setter Property="Background" Value="#F8F8F8"/>
        </Style>
        
        <Style x:Key="DataTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="2"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 标题和控制 -->
            <RowDefinition Height="*"/>    <!-- 主内容 -->
        </Grid.RowDefinitions>

        <!-- 标题和控制 -->
        <StackPanel Grid.Row="0" Orientation="Vertical">
            <TextBlock Text="调试面板" FontSize="16" FontWeight="Bold" 
                       HorizontalAlignment="Center" Margin="5,10"/>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="5">
                <Button Content="清除日志" Command="{Binding ClearLogCommand}" Margin="2"/>
                <Button Content="导出日志" Command="{Binding ExportLogCommand}" Margin="2"/>
                <ToggleButton Content="自动滚动" IsChecked="{Binding AutoScroll}" Margin="2"/>
            </StackPanel>
        </StackPanel>

        <!-- 主内容 -->
        <TabControl Grid.Row="1" Margin="5">
            <!-- 实时数据标签页 -->
            <TabItem Header="实时数据">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 连接信息 -->
                        <GroupBox Header="连接信息" Style="{StaticResource DebugGroupStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="连接状态: "/>
                                    <Run Text="{Binding ConnectionStatus}" FontWeight="Bold"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="连接时间: "/>
                                    <Run Text="{Binding ConnectionTime, StringFormat=yyyy-MM-dd HH:mm:ss}"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="通信延迟: "/>
                                    <Run Text="{Binding CommunicationDelay, StringFormat=F0}"/>
                                    <Run Text=" ms"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="数据包计数: "/>
                                    <Run Text="{Binding PacketCount}"/>
                                </TextBlock>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 位置数据 -->
                        <GroupBox Header="位置数据" Style="{StaticResource DebugGroupStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="T轴位置: "/>
                                    <Run Text="{Binding TAxisPosition, StringFormat=F3}" FontWeight="Bold"/>
                                    <Run Text=" mm"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="R轴角度: "/>
                                    <Run Text="{Binding RAxisPosition, StringFormat=F3}" FontWeight="Bold"/>
                                    <Run Text=" °"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="Z轴高度: "/>
                                    <Run Text="{Binding ZAxisPosition, StringFormat=F3}" FontWeight="Bold"/>
                                    <Run Text=" mm"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="更新频率: "/>
                                    <Run Text="{Binding UpdateFrequency, StringFormat=F1}" FontWeight="Bold"/>
                                    <Run Text=" Hz"/>
                                </TextBlock>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 状态信息 -->
                        <GroupBox Header="状态信息" Style="{StaticResource DebugGroupStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="机器人状态: "/>
                                    <Run Text="{Binding RobotStatus}" FontWeight="Bold"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="Norse ARM: "/>
                                    <Run Text="{Binding NorseArmStatus}" FontWeight="Bold"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="Smooth ARM: "/>
                                    <Run Text="{Binding SmoothArmStatus}" FontWeight="Bold"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="安全状态: "/>
                                    <Run Text="{Binding SafetyStatus}" FontWeight="Bold"/>
                                </TextBlock>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 性能监控 -->
                        <GroupBox Header="性能监控" Style="{StaticResource DebugGroupStyle}">
                            <StackPanel>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="CPU使用率: "/>
                                    <Run Text="{Binding CpuUsage, StringFormat=F1}" FontWeight="Bold"/>
                                    <Run Text=" %"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="内存使用: "/>
                                    <Run Text="{Binding MemoryUsage, StringFormat=F1}" FontWeight="Bold"/>
                                    <Run Text=" MB"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="渲染FPS: "/>
                                    <Run Text="{Binding RenderFps, StringFormat=F1}" FontWeight="Bold"/>
                                </TextBlock>
                                <TextBlock Style="{StaticResource DataTextStyle}">
                                    <Run Text="三角形数: "/>
                                    <Run Text="{Binding TriangleCount}" FontWeight="Bold"/>
                                </TextBlock>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- 通信日志标签页 -->
            <TabItem Header="通信日志">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <TextBlock Text="日志级别:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox SelectedItem="{Binding LogLevel}" Width="80">
                            <ComboBoxItem Content="全部"/>
                            <ComboBoxItem Content="错误"/>
                            <ComboBoxItem Content="警告"/>
                            <ComboBoxItem Content="信息"/>
                            <ComboBoxItem Content="调试"/>
                        </ComboBox>
                        <CheckBox Content="显示时间戳" IsChecked="{Binding ShowTimestamp}" 
                                  VerticalAlignment="Center" Margin="10,0,0,0"/>
                    </StackPanel>
                    
                    <TextBox Grid.Row="1" 
                             Text="{Binding CommunicationLog, Mode=OneWay}"
                             Style="{StaticResource LogTextBoxStyle}"
                             Margin="5"/>
                </Grid>
            </TabItem>
            
            <!-- 错误日志标签页 -->
            <TabItem Header="错误日志">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button Content="清除错误" Command="{Binding ClearErrorsCommand}" Margin="2"/>
                        <TextBlock Text="{Binding ErrorCount, StringFormat=错误数量: {0}}" 
                                   VerticalAlignment="Center" Margin="10,0,0,0" FontWeight="Bold"/>
                    </StackPanel>
                    
                    <TextBox Grid.Row="1" 
                             Text="{Binding ErrorLog, Mode=OneWay}"
                             Style="{StaticResource LogTextBoxStyle}"
                             Margin="5"
                             Foreground="Red"/>
                </Grid>
            </TabItem>
            
            <!-- 寄存器监控标签页 -->
            <TabItem Header="寄存器监控">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <TextBlock Text="寄存器地址:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBox Text="{Binding MonitorRegisterAddress}" Width="80" Margin="0,0,5,0"/>
                        <Button Content="添加监控" Command="{Binding AddRegisterMonitorCommand}" Margin="2"/>
                        <Button Content="清除监控" Command="{Binding ClearRegisterMonitorCommand}" Margin="2"/>
                    </StackPanel>
                    
                    <DataGrid Grid.Row="1" 
                              ItemsSource="{Binding RegisterMonitors}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              Margin="5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="地址" Binding="{Binding Address}" Width="80"/>
                            <DataGridTextColumn Header="当前值" Binding="{Binding CurrentValue}" Width="80"/>
                            <DataGridTextColumn Header="数据类型" Binding="{Binding DataType}" Width="80"/>
                            <DataGridTextColumn Header="更新时间" Binding="{Binding LastUpdate, StringFormat=HH:mm:ss}" Width="80"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
