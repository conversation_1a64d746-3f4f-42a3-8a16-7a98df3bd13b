<UserControl x:Class="Robot3DSimulation.Views.Robot3DView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:helix="http://helix-toolkit.org/wpf"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- 3D视图样式 -->
        <Style x:Key="ViewportStyle" TargetType="helix:HelixViewport3D">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="ShowCoordinateSystem" Value="True"/>
            <Setter Property="ShowViewCube" Value="True"/>
            <Setter Property="ShowFrameRate" Value="True"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 工具栏 -->
            <RowDefinition Height="*"/>    <!-- 3D视图 -->
            <RowDefinition Height="Auto"/> <!-- 状态栏 -->
        </Grid.RowDefinitions>

        <!-- 3D视图工具栏 -->
        <ToolBar Grid.Row="0" Background="#F8F8F8">
            <Button Command="{Binding ResetCameraCommand}" ToolTip="重置视角">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🎥" FontSize="14" Margin="0,0,3,0"/>
                    <TextBlock Text="重置视角"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <ToggleButton IsChecked="{Binding ShowAxes}" ToolTip="显示坐标轴">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📐" FontSize="14" Margin="0,0,3,0"/>
                    <TextBlock Text="坐标轴"/>
                </StackPanel>
            </ToggleButton>
            
            <ToggleButton IsChecked="{Binding ShowGrid}" ToolTip="显示网格">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="⊞" FontSize="14" Margin="0,0,3,0"/>
                    <TextBlock Text="网格"/>
                </StackPanel>
            </ToggleButton>
            
            <ToggleButton IsChecked="{Binding ShowTrajectory}" ToolTip="显示轨迹">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📈" FontSize="14" Margin="0,0,3,0"/>
                    <TextBlock Text="轨迹"/>
                </StackPanel>
            </ToggleButton>
            
            <Separator/>
            
            <ComboBox SelectedItem="{Binding ViewMode}" Width="100" ToolTip="视图模式">
                <ComboBoxItem Content="透视"/>
                <ComboBoxItem Content="正交"/>
            </ComboBox>
            
            <ComboBox SelectedItem="{Binding RenderQuality}" Width="80" ToolTip="渲染质量">
                <ComboBoxItem Content="高"/>
                <ComboBoxItem Content="中"/>
                <ComboBoxItem Content="低"/>
            </ComboBox>
            
            <Separator/>
            
            <Button Command="{Binding ZoomToFitCommand}" ToolTip="缩放到适合">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔍" FontSize="14" Margin="0,0,3,0"/>
                    <TextBlock Text="适合"/>
                </StackPanel>
            </Button>
            
            <Button Command="{Binding ScreenshotCommand}" ToolTip="截图">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📷" FontSize="14" Margin="0,0,3,0"/>
                    <TextBlock Text="截图"/>
                </StackPanel>
            </Button>
        </ToolBar>

        <!-- 3D视图区域 -->
        <Border Grid.Row="1" BorderBrush="#CCCCCC" BorderThickness="1">
            <helix:HelixViewport3D x:Name="Viewport3D" 
                                   Style="{StaticResource ViewportStyle}"
                                   Camera="{Binding Camera}"
                                   ShowCoordinateSystem="{Binding ShowAxes}"
                                   ShowViewCube="True"
                                   ShowFrameRate="True"
                                   ZoomExtentsWhenLoaded="True">
                
                <!-- 环境光 -->
                <helix:DefaultLights/>
                
                <!-- 网格 -->
                <helix:GridLinesVisual3D x:Name="GridLines"
                                         Visible="{Binding ShowGrid}"
                                         Center="0,0,0"
                                         Width="1000"
                                         Length="1000"
                                         MinorDistance="50"
                                         MajorDistance="100"
                                         Thickness="1"
                                         Fill="#40808080"/>
                
                <!-- 坐标轴 -->
                <helix:CoordinateSystemVisual3D x:Name="CoordinateSystem"
                                                ArrowLengths="100"/>
                
                <!-- 机器人模型容器 -->
                <ModelVisual3D x:Name="RobotModel">
                    <ModelVisual3D.Content>
                        <Model3DGroup x:Name="RobotModelGroup">
                            <!-- 机器人3D模型将在代码中动态添加 -->
                        </Model3DGroup>
                    </ModelVisual3D.Content>
                </ModelVisual3D>
                
                <!-- 轨迹显示 -->
                <helix:LinesVisual3D x:Name="TrajectoryLines"
                                     Points="{Binding TrajectoryPoints}"
                                     Color="Red"
                                     Thickness="2"/>
                
                <!-- 当前位置标记 -->
                <helix:SphereVisual3D x:Name="CurrentPositionMarker"
                                      Center="{Binding CurrentPosition}"
                                      Radius="5"
                                      Fill="Yellow"/>
                
                <!-- 目标位置标记 -->
                <helix:SphereVisual3D x:Name="TargetPositionMarker"
                                      Center="{Binding TargetPosition}"
                                      Radius="3"
                                      Fill="Green"
                                      Visible="{Binding HasTargetPosition}"/>
            </helix:HelixViewport3D>
        </Border>

        <!-- 3D视图状态栏 -->
        <StatusBar Grid.Row="2" Background="#F0F0F0">
            <StatusBarItem>
                <TextBlock Text="{Binding CameraInfo}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding RenderInfo}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="FPS: "/>
                    <TextBlock Text="{Binding FrameRate, StringFormat=F1}"/>
                </StackPanel>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="三角形: "/>
                    <TextBlock Text="{Binding TriangleCount}"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="鼠标: "/>
                    <TextBlock Text="{Binding MousePosition}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>
