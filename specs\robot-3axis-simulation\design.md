# 设计文档 - Robot三轴虚拟仿真调试系统

## 系统架构概述

本系统基于现有的WPF计算器项目架构，采用MVVM模式和依赖注入，集成3D可视化和Modbus通信功能。系统将扩展现有架构以支持实时3D机器人仿真。

## 技术栈扩展

### 新增依赖项
- **HelixToolkit.Wpf** - 3D可视化渲染引擎
- **NModbus** - Modbus通信协议实现
- **System.IO.Ports** - 串口通信支持
- **Microsoft.Extensions.Logging** - 日志记录
- **Newtonsoft.Json** - 配置文件序列化

### 核心技术
- WPF 3D Graphics (基于HelixToolkit)
- Modbus TCP/RTU 通信
- 多线程数据处理
- 实时数据绑定

## 架构设计

### 文件夹结构扩展

```
/Robot3D/
├── Models/
│   ├── RobotModel.cs              # 机器人数据模型
│   ├── AxisPosition.cs            # 轴位置数据
│   ├── RobotConfiguration.cs      # 机器人配置
│   └── ModbusConfiguration.cs     # Modbus配置
├── Services/
│   ├── IRobotService.cs           # 机器人服务接口
│   ├── RobotService.cs            # 机器人业务逻辑
│   ├── IModbusService.cs          # Modbus服务接口
│   ├── ModbusService.cs           # Modbus通信实现
│   ├── I3DRenderService.cs        # 3D渲染服务接口
│   └── Robot3DRenderService.cs    # 3D渲染实现
├── ViewModels/
│   ├── Robot3DViewModel.cs        # 3D视图模型
│   ├── RobotControlViewModel.cs   # 控制面板视图模型
│   └── DebugPanelViewModel.cs     # 调试面板视图模型
├── Views/
│   ├── Robot3DView.xaml           # 3D可视化界面
│   ├── RobotControlPanel.xaml     # 控制面板
│   └── DebugPanel.xaml            # 调试信息面板
└── Helpers/
    ├── CoordinateConverter.cs      # 坐标转换工具
    ├── SafetyValidator.cs          # 安全范围验证
    └── TrajectoryRecorder.cs       # 轨迹记录器
```

## 核心组件设计

### 1. 数据模型层

#### RobotModel.cs
```csharp
/// <summary>
/// 机器人主数据模型，包含三轴位置和ARM状态
/// </summary>
public partial class RobotModel : ObservableObject
{
    [ObservableProperty] private AxisPosition tAxis;    // T轴（平移轴）
    [ObservableProperty] private AxisPosition rAxis;    // R轴（旋转轴）
    [ObservableProperty] private AxisPosition zAxis;    // Z轴（垂直轴）
    [ObservableProperty] private ArmStatus norseEnd;    // Norse端状态
    [ObservableProperty] private ArmStatus smoothEnd;   // Smooth端状态
    [ObservableProperty] private bool isConnected;      // 连接状态
    [ObservableProperty] private string statusMessage;  // 状态信息
}
```

#### AxisPosition.cs
```csharp
/// <summary>
/// 轴位置数据模型
/// </summary>
public partial class AxisPosition : ObservableObject
{
    [ObservableProperty] private double currentPosition;  // 当前位置
    [ObservableProperty] private double targetPosition;   // 目标位置
    [ObservableProperty] private double minLimit;         // 最小限位
    [ObservableProperty] private double maxLimit;         // 最大限位
    [ObservableProperty] private bool isInSafeRange;      // 是否在安全范围内
    [ObservableProperty] private DateTime lastUpdate;     // 最后更新时间
}
```

### 2. 服务层设计

#### IRobotService.cs
```csharp
/// <summary>
/// 机器人服务接口，定义核心业务逻辑
/// </summary>
public interface IRobotService
{
    Task<bool> ConnectAsync();
    Task DisconnectAsync();
    Task<bool> MoveToPositionAsync(double t, double r, double z);
    Task<AxisPosition[]> GetCurrentPositionsAsync();
    event EventHandler<RobotPositionChangedEventArgs> PositionChanged;
    bool IsConnected { get; }
}
```

#### IModbusService.cs
```csharp
/// <summary>
/// Modbus通信服务接口
/// </summary>
public interface IModbusService
{
    Task<bool> ConnectAsync(ModbusConfiguration config);
    Task DisconnectAsync();
    Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count);
    Task WriteHoldingRegistersAsync(ushort startAddress, ushort[] values);
    bool IsConnected { get; }
}
```

### 3. 3D渲染设计

#### I3DRenderService.cs
```csharp
/// <summary>
/// 3D渲染服务接口
/// </summary>
public interface I3DRenderService
{
    void InitializeScene(Viewport3D viewport);
    void UpdateRobotPosition(AxisPosition tAxis, AxisPosition rAxis, AxisPosition zAxis);
    void ShowTrajectory(List<Point3D> trajectoryPoints);
    void SetCameraView(Point3D position, Vector3D lookDirection);
    void HighlightAxis(string axisName, bool highlight);
}
```

### 4. ViewModel设计

#### Robot3DViewModel.cs
```csharp
/// <summary>
/// 3D视图的主视图模型
/// </summary>
public partial class Robot3DViewModel : ObservableObject
{
    private readonly IRobotService robotService;
    private readonly I3DRenderService renderService;
    
    [ObservableProperty] private RobotModel robot;
    [ObservableProperty] private bool isAutoUpdate;
    [ObservableProperty] private int updateFrequency;
    
    [RelayCommand] private async Task ConnectRobot();
    [RelayCommand] private async Task DisconnectRobot();
    [RelayCommand] private void ResetCamera();
    [RelayCommand] private void ToggleTrajectory();
}
```

## 通信协议设计

### Modbus寄存器映射
```
地址范围: 40001-40020
40001-40002: T轴当前位置 (32位浮点数)
40003-40004: R轴当前位置 (32位浮点数)  
40005-40006: Z轴当前位置 (32位浮点数)
40007-40008: T轴目标位置 (32位浮点数)
40009-40010: R轴目标位置 (32位浮点数)
40011-40012: Z轴目标位置 (32位浮点数)
40013: Norse端状态 (16位)
40014: Smooth端状态 (16位)
40015: 系统状态字 (16位)
40016-40020: 保留
```

### 数据更新策略
- 读取频率: 100ms (可配置)
- 异常重连: 指数退避算法
- 数据验证: 范围检查和变化率限制

## 3D场景设计

### 坐标系统
- X轴: 水平方向 (T轴对应)
- Y轴: 深度方向
- Z轴: 垂直方向 (Z轴对应)
- R轴: 绕Z轴旋转

### 3D模型组件
1. **机器人底座**: 固定基座模型
2. **T轴滑台**: 水平移动平台
3. **R轴转台**: 旋转平台
4. **Z轴升降**: 垂直移动机构
5. **ARM机械臂**: 包含Norse端和Smooth端
6. **坐标轴**: 可视化坐标参考
7. **安全区域**: 半透明边界显示

### 视觉效果
- 材质: 金属质感着色
- 光照: 环境光 + 定向光
- 动画: 平滑插值运动
- 高亮: 异常状态红色显示

## 性能优化策略

### 渲染优化
- LOD (Level of Detail): 根据距离调整模型精度
- 视锥剔除: 只渲染可见对象
- 帧率控制: 自适应质量调整
- 批量更新: 减少UI线程调用

### 内存管理
- 轨迹数据: 循环缓冲区限制历史点数
- 3D资源: 及时释放不用的几何体
- 事件订阅: 弱引用避免内存泄漏

### 多线程设计
- UI线程: 仅处理界面更新
- 通信线程: 独立的Modbus通信
- 渲染线程: 3D场景计算
- 数据线程: 位置数据处理

## 配置管理

### 配置文件结构 (JSON)
```json
{
  "modbus": {
    "connectionType": "TCP",
    "ipAddress": "*************",
    "port": 502,
    "slaveId": 1,
    "timeout": 5000
  },
  "robot": {
    "tAxis": { "min": -1000, "max": 1000, "unit": "mm" },
    "rAxis": { "min": -360, "max": 360, "unit": "degree" },
    "zAxis": { "min": 0, "max": 500, "unit": "mm" }
  },
  "rendering": {
    "updateFrequency": 100,
    "maxTrajectoryPoints": 1000,
    "autoQuality": true
  }
}
```

## 错误处理策略

### 异常类型
- `RobotCommunicationException`: 通信异常
- `RobotSafetyException`: 安全范围异常  
- `RobotConfigurationException`: 配置异常
- `Robot3DRenderException`: 渲染异常

### 错误恢复
1. 通信中断: 自动重连机制
2. 数据异常: 使用上一次有效值
3. 渲染错误: 降级到2D显示
4. 配置错误: 回退到默认配置

## 集成方案

### 与现有架构集成
1. 扩展现有的依赖注入容器
2. 添加新的服务注册
3. 创建独立的Robot模块窗口
4. 保持现有计算器功能不受影响

### 启动流程
1. 应用程序启动时注册Robot服务
2. 用户选择打开Robot仿真模块
3. 初始化3D场景和Modbus连接
4. 开始实时数据更新循环

这个设计确保了系统的可扩展性、可维护性和性能，同时与现有的WPF计算器架构保持一致。