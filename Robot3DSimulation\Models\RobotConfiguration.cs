using System;

namespace Robot3DSimulation.Models
{
    /// <summary>
    /// 渲染质量枚举
    /// </summary>
    public enum RenderQuality
    {
        Low,
        Medium,
        High,
        Ultra
    }

    /// <summary>
    /// 抗锯齿模式枚举
    /// </summary>
    public enum AntiAliasingMode
    {
        None,
        FXAA,
        MSAA2x,
        MSAA4x,
        MSAA8x
    }

    /// <summary>
    /// 机器人配置模型
    /// </summary>
    public class RobotConfiguration
    {
        /// <summary>
        /// T轴配置
        /// </summary>
        public AxisConfiguration TAxis { get; set; } = new();

        /// <summary>
        /// R轴配置
        /// </summary>
        public AxisConfiguration RAxis { get; set; } = new();

        /// <summary>
        /// Z轴配置
        /// </summary>
        public AxisConfiguration ZAxis { get; set; } = new();

        /// <summary>
        /// 更新频率（毫秒）
        /// </summary>
        public int UpdateFrequency { get; set; } = 100;

        /// <summary>
        /// 最大轨迹点数
        /// </summary>
        public int MaxTrajectoryPoints { get; set; } = 1000;

        /// <summary>
        /// 是否启用安全检查
        /// </summary>
        public bool EnableSafetyCheck { get; set; } = true;

        /// <summary>
        /// 移动超时时间（毫秒）
        /// </summary>
        public int MoveTimeout { get; set; } = 30000;

        /// <summary>
        /// 位置到达容差
        /// </summary>
        public double PositionTolerance { get; set; } = 0.1;

        /// <summary>
        /// 轨迹缓冲区大小
        /// </summary>
        public int TrajectoryBufferSize { get; set; } = 1000;
    }

    /// <summary>
    /// 轴配置
    /// </summary>
    public class AxisConfiguration
    {
        /// <summary>
        /// 轴名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 最小限位
        /// </summary>
        public double Min { get; set; }

        /// <summary>
        /// 最大限位
        /// </summary>
        public double Max { get; set; }

        /// <summary>
        /// 最小限位（兼容性属性）
        /// </summary>
        public double MinLimit => Min;

        /// <summary>
        /// 最大限位（兼容性属性）
        /// </summary>
        public double MaxLimit => Max;

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 安全余量
        /// </summary>
        public double SafetyMargin { get; set; }

        /// <summary>
        /// 最大速度
        /// </summary>
        public double MaxVelocity { get; set; } = 100;

        /// <summary>
        /// 最大加速度
        /// </summary>
        public double MaxAcceleration { get; set; } = 50;

        /// <summary>
        /// 默认速度
        /// </summary>
        public double DefaultVelocity { get; set; } = 50;
    }

    /// <summary>
    /// 渲染配置
    /// </summary>
    public class RenderingConfiguration
    {
        /// <summary>
        /// 自动质量调整
        /// </summary>
        public bool AutoQuality { get; set; } = true;

        /// <summary>
        /// 渲染质量
        /// </summary>
        public RenderQuality Quality { get; set; } = RenderQuality.High;

        /// <summary>
        /// 启用反射
        /// </summary>
        public bool EnableReflections { get; set; } = true;

        /// <summary>
        /// 抗锯齿设置
        /// </summary>
        public AntiAliasingMode AntiAliasing { get; set; } = AntiAliasingMode.MSAA4x;

        /// <summary>
        /// 目标帧率
        /// </summary>
        public int TargetFrameRate { get; set; } = 30;

        /// <summary>
        /// 最大渲染距离
        /// </summary>
        public double MaxRenderDistance { get; set; } = 2000.0;

        /// <summary>
        /// 启用抗锯齿
        /// </summary>
        public bool EnableAntiAliasing { get; set; } = true;

        /// <summary>
        /// 启用阴影
        /// </summary>
        public bool EnableShadows { get; set; } = true;

        /// <summary>
        /// 光照强度
        /// </summary>
        public double LightIntensity { get; set; } = 1.0;

        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackgroundColor { get; set; } = "#F0F0F0";

        /// <summary>
        /// 网格显示
        /// </summary>
        public bool ShowGrid { get; set; } = true;

        /// <summary>
        /// 坐标轴显示
        /// </summary>
        public bool ShowAxes { get; set; } = true;
    }

    /// <summary>
    /// 调试配置
    /// </summary>
    public class DebugConfiguration
    {
        /// <summary>
        /// 启用调试面板
        /// </summary>
        public bool EnableDebugPanel { get; set; } = true;

        /// <summary>
        /// 记录到文件
        /// </summary>
        public bool LogToFile { get; set; } = true;

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "logs/robot3d.log";

        /// <summary>
        /// 最大日志文件大小（字节）
        /// </summary>
        public long MaxLogFileSize { get; set; } = 10 * 1024 * 1024; // 10MB

        /// <summary>
        /// 启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// 性能数据保留时间（分钟）
        /// </summary>
        public int PerformanceDataRetentionMinutes { get; set; } = 60;
    }
}
