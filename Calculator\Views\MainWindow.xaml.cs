﻿using System;
using System.Windows;
using Calculator.ViewModels;

namespace Calculator
{
    /// <summary>
    /// 计算器主窗口
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// 初始化MainWindow
        /// </summary>
        /// <param name="viewModel">计算器视图模型</param>
        public MainWindow(CalculatorViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        }
    }
}