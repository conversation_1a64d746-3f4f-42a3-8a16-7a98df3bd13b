using System;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media.Media3D;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HelixToolkit.Wpf;
using Microsoft.Extensions.Logging;
using Robot3DSimulation.Models;
using Robot3DSimulation.Services;

namespace Robot3DSimulation.ViewModels
{
    /// <summary>
    /// 3D视图模型
    /// </summary>
    public partial class Robot3DViewModel : ObservableObject
    {
        private readonly ILogger<Robot3DViewModel> _logger;
        private readonly IRobotService _robotService;
        private readonly I3DRenderService _renderService;
        private readonly IConfigurationService _configService;
        private HelixViewport3D? _viewport;
        private bool _disposed;

        [ObservableProperty]
        private bool _isInitialized;

        [ObservableProperty]
        private bool _showGrid = true;

        [ObservableProperty]
        private bool _showCoordinateSystem = true;

        [ObservableProperty]
        private bool _showTrajectory = true;

        [ObservableProperty]
        private bool _showRobot = true;

        [ObservableProperty]
        private string _viewMode = "Perspective";

        [ObservableProperty]
        private string _renderQuality = "High";

        [ObservableProperty]
        private double _cameraX = 500;

        [ObservableProperty]
        private double _cameraY = 500;

        [ObservableProperty]
        private double _cameraZ = 300;

        [ObservableProperty]
        private double _targetX = 0;

        [ObservableProperty]
        private double _targetY = 0;

        [ObservableProperty]
        private double _targetZ = 0;

        [ObservableProperty]
        private bool _enableShadows = true;

        [ObservableProperty]
        private bool _enableReflections = false;

        [ObservableProperty]
        private bool _antiAliasing = true;

        [ObservableProperty]
        private string _backgroundColor = "#F5F5F5";

        [ObservableProperty]
        private int _triangleCount;

        [ObservableProperty]
        private double _frameRate;

        [ObservableProperty]
        private bool _isAnimating;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="robotService">机器人服务</param>
        /// <param name="renderService">3D渲染服务</param>
        /// <param name="configService">配置服务</param>
        public Robot3DViewModel(
            ILogger<Robot3DViewModel> logger,
            IRobotService robotService,
            I3DRenderService renderService,
            IConfigurationService configService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _robotService = robotService ?? throw new ArgumentNullException(nameof(robotService));
            _renderService = renderService ?? throw new ArgumentNullException(nameof(renderService));
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));

            // 订阅服务事件
            SubscribeToServiceEvents();

            // 从配置加载设置
            LoadSettingsFromConfiguration();

            _logger.LogInformation("3D视图模型已初始化");
        }

        /// <summary>
        /// 初始化3D视图命令
        /// </summary>
        [RelayCommand]
        private async Task InitializeViewAsync()
        {
            try
            {
                if (_viewport == null)
                {
                    _logger.LogWarning("视口未设置，无法初始化3D视图");
                    return;
                }

                _logger.LogInformation("正在初始化3D视图...");

                await _renderService.InitializeSceneAsync(_viewport.Viewport);
                IsInitialized = true;

                _logger.LogInformation("3D视图初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化3D视图时发生错误");
            }
        }

        /// <summary>
        /// 重置视图命令
        /// </summary>
        [RelayCommand]
        private async Task ResetViewAsync()
        {
            try
            {
                var defaultPosition = new Point3D(500, 500, 300);
                var defaultDirection = new Vector3D(-1, -1, -0.5);
                var defaultUp = new Vector3D(0, 0, 1);

                await _renderService.SetCameraAsync(defaultPosition, defaultDirection, defaultUp);

                CameraX = defaultPosition.X;
                CameraY = defaultPosition.Y;
                CameraZ = defaultPosition.Z;

                _logger.LogInformation("视图已重置");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置视图时发生错误");
            }
        }

        /// <summary>
        /// 切换网格显示命令
        /// </summary>
        [RelayCommand]
        private async Task ToggleGridAsync()
        {
            try
            {
                ShowGrid = !ShowGrid;
                // 这里应该调用渲染服务来切换网格显示
                _logger.LogInformation("网格显示已{Status}", ShowGrid ? "开启" : "关闭");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换网格显示时发生错误");
            }
        }

        /// <summary>
        /// 切换坐标系显示命令
        /// </summary>
        [RelayCommand]
        private async Task ToggleCoordinateSystemAsync()
        {
            try
            {
                ShowCoordinateSystem = !ShowCoordinateSystem;
                // 这里应该调用渲染服务来切换坐标系显示
                _logger.LogInformation("坐标系显示已{Status}", ShowCoordinateSystem ? "开启" : "关闭");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换坐标系显示时发生错误");
            }
        }

        /// <summary>
        /// 切换轨迹显示命令
        /// </summary>
        [RelayCommand]
        private async Task ToggleTrajectoryAsync()
        {
            try
            {
                ShowTrajectory = !ShowTrajectory;
                // 这里应该调用渲染服务来切换轨迹显示
                _logger.LogInformation("轨迹显示已{Status}", ShowTrajectory ? "开启" : "关闭");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换轨迹显示时发生错误");
            }
        }

        /// <summary>
        /// 清除轨迹命令
        /// </summary>
        [RelayCommand]
        private async Task ClearTrajectoryAsync()
        {
            try
            {
                await _renderService.ClearTrajectoryAsync();
                _robotService.ClearTrajectory();
                _logger.LogInformation("轨迹已清除");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除轨迹时发生错误");
            }
        }

        /// <summary>
        /// 开始旋转动画命令
        /// </summary>
        [RelayCommand]
        private async Task StartRotationAnimationAsync()
        {
            try
            {
                if (IsAnimating)
                {
                    await _renderService.StopAnimationAsync();
                    IsAnimating = false;
                    _logger.LogInformation("动画已停止");
                }
                else
                {
                    // 获取当前位置作为起始位置
                    var currentPos = _robotService.GetCurrentPosition();
                    // 创建一个旋转动画：R轴旋转360度
                    await _renderService.StartAnimationAsync(
                        currentPos.T, currentPos.R, currentPos.Z,  // 起始位置
                        currentPos.T, currentPos.R + 360, currentPos.Z,  // 结束位置（R轴+360度）
                        10000);  // 10秒
                    IsAnimating = true;
                    _logger.LogInformation("旋转动画已开始");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "控制旋转动画时发生错误");
            }
        }

        /// <summary>
        /// 设置视图模式命令
        /// </summary>
        [RelayCommand]
        private async Task SetViewModeAsync(string mode)
        {
            try
            {
                ViewMode = mode;
                
                switch (mode.ToLower())
                {
                    case "front":
                        await _renderService.SetCameraAsync(
                            new Point3D(0, -1000, 100),
                            new Vector3D(0, 1, 0),
                            new Vector3D(0, 0, 1));
                        break;
                    case "side":
                        await _renderService.SetCameraAsync(
                            new Point3D(1000, 0, 100),
                            new Vector3D(-1, 0, 0),
                            new Vector3D(0, 0, 1));
                        break;
                    case "top":
                        await _renderService.SetCameraAsync(
                            new Point3D(0, 0, 1000),
                            new Vector3D(0, 0, -1),
                            new Vector3D(0, 1, 0));
                        break;
                    case "perspective":
                        await _renderService.SetCameraAsync(
                            new Point3D(500, 500, 300),
                            new Vector3D(-1, -1, -0.5),
                            new Vector3D(0, 0, 1));
                        break;
                }

                _logger.LogInformation("视图模式已设置为: {Mode}", mode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置视图模式时发生错误");
            }
        }

        /// <summary>
        /// 设置渲染质量命令
        /// </summary>
        [RelayCommand]
        private async Task SetRenderQualityAsync(string quality)
        {
            try
            {
                RenderQuality = quality;
                // 将字符串质量转换为数值
                var qualityLevel = quality switch
                {
                    "Low" => 1,
                    "Medium" => 2,
                    "High" => 3,
                    "Ultra" => 4,
                    _ => 2
                };
                await _renderService.SetRenderQualityAsync(qualityLevel);
                _logger.LogInformation("渲染质量已设置为: {Quality}", quality);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置渲染质量时发生错误");
            }
        }

        /// <summary>
        /// 导出场景命令
        /// </summary>
        [RelayCommand]
        private async Task ExportSceneAsync()
        {
            try
            {
                var fileName = $"Scene_{DateTime.Now:yyyyMMdd_HHmmss}.obj";
                await _renderService.ExportSceneAsync(fileName);
                _logger.LogInformation("场景已导出到: {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出场景时发生错误");
            }
        }

        /// <summary>
        /// 设置视口
        /// </summary>
        /// <param name="viewport">3D视口</param>
        public void SetViewport(HelixViewport3D viewport)
        {
            _viewport = viewport ?? throw new ArgumentNullException(nameof(viewport));
            _logger.LogInformation("视口已设置");
        }

        /// <summary>
        /// 订阅服务事件
        /// </summary>
        private void SubscribeToServiceEvents()
        {
            try
            {
                // 机器人位置变化事件
                _robotService.PositionChanged += OnRobotPositionChanged;
                _robotService.TrajectoryRecorded += OnTrajectoryRecorded;

                // 渲染统计事件
                _renderService.RenderStatsUpdated += OnRenderStatsUpdated;
                _renderService.CameraChanged += OnCameraChanged;
                _renderService.SceneInitialized += OnSceneInitialized;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "订阅服务事件时发生错误");
            }
        }

        /// <summary>
        /// 从配置加载设置
        /// </summary>
        private void LoadSettingsFromConfiguration()
        {
            try
            {
                var renderConfig = _configService.RenderingConfig;

                RenderQuality = renderConfig.Quality.ToString();
                EnableShadows = renderConfig.EnableShadows;
                EnableReflections = renderConfig.EnableReflections;
                AntiAliasing = renderConfig.AntiAliasing == Models.AntiAliasingMode.MSAA4x;
                BackgroundColor = renderConfig.BackgroundColor;

                _logger.LogInformation("已从配置加载3D视图设置");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从配置加载设置时发生错误");
            }
        }

        /// <summary>
        /// 机器人位置变化处理
        /// </summary>
        private async void OnRobotPositionChanged(object? sender, RobotPositionChangedEventArgs e)
        {
            try
            {
                if (!IsInitialized)
                    return;

                var robotModel = _robotService.Robot;
                await _renderService.UpdateRobotPositionAsync(
                    robotModel.TAxis,
                    robotModel.RAxis,
                    robotModel.ZAxis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理机器人位置变化时发生错误");
            }
        }

        /// <summary>
        /// 轨迹记录处理
        /// </summary>
        private async void OnTrajectoryRecorded(object? sender, TrajectoryRecordedEventArgs e)
        {
            try
            {
                if (!IsInitialized || !ShowTrajectory)
                    return;

                await _renderService.AddTrajectoryPointAsync(e.TrajectoryPoint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理轨迹记录时发生错误");
            }
        }

        /// <summary>
        /// 渲染统计更新处理
        /// </summary>
        private void OnRenderStatsUpdated(object? sender, string statsText)
        {
            try
            {
                // 解析统计文本，提取帧率和三角形数量
                var lines = statsText.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains("帧率:"))
                    {
                        var fpsText = line.Split(':')[1].Replace("FPS", "").Trim();
                        if (double.TryParse(fpsText, out var fps))
                            FrameRate = fps;
                    }
                    else if (line.Contains("三角形数量:"))
                    {
                        var triangleText = line.Split(':')[1].Trim();
                        if (int.TryParse(triangleText, out var triangles))
                            TriangleCount = triangles;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理渲染统计更新时发生错误");
            }
        }

        /// <summary>
        /// 相机变化处理
        /// </summary>
        private void OnCameraChanged(object? sender, string cameraInfo)
        {
            try
            {
                _logger.LogInformation("相机状态变化: {CameraInfo}", cameraInfo);
                // 这里可以解析相机信息字符串来更新相机位置
                // 暂时只记录日志
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理相机变化时发生错误");
            }
        }

        /// <summary>
        /// 场景初始化完成处理
        /// </summary>
        private void OnSceneInitialized(object? sender, bool isInitialized)
        {
            try
            {
                _logger.LogInformation("3D场景初始化完成");

                // 如果机器人已连接，更新初始位置
                if (_robotService.IsConnected)
                {
                    var robotModel = _robotService.Robot;
                    _ = Task.Run(async () => await _renderService.UpdateRobotPositionAsync(
                        robotModel.TAxis,
                        robotModel.RAxis,
                        robotModel.ZAxis));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理场景初始化完成时发生错误");
            }
        }

        /// <summary>
        /// 更新相机位置
        /// </summary>
        partial void OnCameraXChanged(double value) => UpdateCameraPosition();
        partial void OnCameraYChanged(double value) => UpdateCameraPosition();
        partial void OnCameraZChanged(double value) => UpdateCameraPosition();

        /// <summary>
        /// 更新相机位置
        /// </summary>
        private async void UpdateCameraPosition()
        {
            try
            {
                if (!IsInitialized)
                    return;

                var position = new Point3D(CameraX, CameraY, CameraZ);
                var target = new Point3D(TargetX, TargetY, TargetZ);
                var direction = target - position;
                direction.Normalize();

                await _renderService.SetCameraAsync(position, direction, new Vector3D(0, 0, 1));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新相机位置时发生错误");
            }
        }

        /// <summary>
        /// 渲染质量变化处理
        /// </summary>
        partial void OnRenderQualityChanged(string value)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    // 将字符串质量转换为数值
                    var qualityLevel = value switch
                    {
                        "Low" => 1,
                        "Medium" => 2,
                        "High" => 3,
                        "Ultra" => 4,
                        _ => 2
                    };
                    await _renderService.SetRenderQualityAsync(qualityLevel);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新渲染质量时发生错误");
                }
            });
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                // 取消订阅事件
                if (_robotService != null)
                {
                    _robotService.PositionChanged -= OnRobotPositionChanged;
                    _robotService.TrajectoryRecorded -= OnTrajectoryRecorded;
                }

                if (_renderService != null)
                {
                    _renderService.RenderStatsUpdated -= OnRenderStatsUpdated;
                    _renderService.CameraChanged -= OnCameraChanged;
                    _renderService.SceneInitialized -= OnSceneInitialized;
                }

                _disposed = true;
                _logger.LogInformation("3D视图模型已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放3D视图模型时发生错误");
            }
        }
    }
}
