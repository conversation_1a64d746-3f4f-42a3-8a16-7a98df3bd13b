#nullable enable

using System;
using Xunit;
using Calculator.Services;
using Calculator.Enums;
using Calculator.Exceptions;

namespace Calculator.Tests
{
    /// <summary>
    /// CalculatorService的单元测试
    /// </summary>
    public class CalculatorServiceTests
    {
        private readonly ICalculatorService _calculatorService;

        public CalculatorServiceTests()
        {
            _calculatorService = new CalculatorService();
        }

        #region 基本运算测试

        [Theory]
        [InlineData(5, 3, 8)]
        [InlineData(-5, 3, -2)]
        [InlineData(0, 0, 0)]
        [InlineData(1.5, 2.5, 4.0)]
        public void Calculate_Addition_ReturnsCorrectResult(double operand1, double operand2, double expected)
        {
            // Act
            var result = _calculatorService.Calculate(operand1, operand2, OperationType.Addition);

            // Assert
            Assert.Equal(expected, result, 10);
        }

        [Theory]
        [InlineData(5, 3, 2)]
        [InlineData(-5, 3, -8)]
        [InlineData(0, 5, -5)]
        [InlineData(2.5, 1.5, 1.0)]
        public void Calculate_Subtraction_ReturnsCorrectResult(double operand1, double operand2, double expected)
        {
            // Act
            var result = _calculatorService.Calculate(operand1, operand2, OperationType.Subtraction);

            // Assert
            Assert.Equal(expected, result, 10);
        }

        [Theory]
        [InlineData(5, 3, 15)]
        [InlineData(-5, 3, -15)]
        [InlineData(0, 5, 0)]
        [InlineData(2.5, 2, 5.0)]
        public void Calculate_Multiplication_ReturnsCorrectResult(double operand1, double operand2, double expected)
        {
            // Act
            var result = _calculatorService.Calculate(operand1, operand2, OperationType.Multiplication);

            // Assert
            Assert.Equal(expected, result, 10);
        }

        [Theory]
        [InlineData(6, 3, 2)]
        [InlineData(-6, 3, -2)]
        [InlineData(0, 5, 0)]
        [InlineData(5.0, 2.0, 2.5)]
        public void Calculate_Division_ReturnsCorrectResult(double operand1, double operand2, double expected)
        {
            // Act
            var result = _calculatorService.Calculate(operand1, operand2, OperationType.Division);

            // Assert
            Assert.Equal(expected, result, 10);
        }

        #endregion

        #region 异常处理测试

        [Fact]
        public void Calculate_DivisionByZero_ThrowsCalculatorException()
        {
            // Act & Assert
            var exception = Assert.Throws<CalculatorException>(() =>
                _calculatorService.Calculate(5, 0, OperationType.Division));

            Assert.Equal("除数不能为零", exception.Message);
        }

        [Fact]
        public void Calculate_InvalidOperationType_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() =>
                _calculatorService.Calculate(5, 3, (OperationType)999));
        }

        #endregion

        #region 数字解析测试

        [Theory]
        [InlineData("123", true, 123)]
        [InlineData("123.45", true, 123.45)]
        [InlineData("-123.45", true, -123.45)]
        [InlineData("0", true, 0)]
        [InlineData("0.0", true, 0.0)]
        public void TryParseNumber_ValidNumbers_ReturnsTrue(string input, bool expectedSuccess, double expectedValue)
        {
            // Act
            var success = _calculatorService.TryParseNumber(input, out var result);

            // Assert
            Assert.Equal(expectedSuccess, success);
            if (expectedSuccess)
            {
                Assert.Equal(expectedValue, result, 10);
            }
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData("abc")]
        [InlineData("12.34.56")]
        [InlineData("∞")]
        public void TryParseNumber_InvalidNumbers_ReturnsFalse(string input)
        {
            // Act
            var success = _calculatorService.TryParseNumber(input, out var result);

            // Assert
            Assert.False(success);
            Assert.Equal(0, result);
        }

        #endregion

        #region 操作验证测试

        [Theory]
        [InlineData(5, 3, OperationType.Addition, true)]
        [InlineData(5, 3, OperationType.Subtraction, true)]
        [InlineData(5, 3, OperationType.Multiplication, true)]
        [InlineData(5, 3, OperationType.Division, true)]
        [InlineData(5, 0, OperationType.Division, false)]
        public void IsValidOperation_VariousInputs_ReturnsExpectedResult(
            double operand1, double operand2, OperationType operation, bool expected)
        {
            // Act
            var result = _calculatorService.IsValidOperation(operand1, operand2, operation);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData(double.PositiveInfinity, 5, OperationType.Addition, false)]
        [InlineData(5, double.NaN, OperationType.Addition, false)]
        [InlineData(double.NegativeInfinity, 5, OperationType.Multiplication, false)]
        public void IsValidOperation_InvalidNumbers_ReturnsFalse(
            double operand1, double operand2, OperationType operation, bool expected)
        {
            // Act
            var result = _calculatorService.IsValidOperation(operand1, operand2, operation);

            // Assert
            Assert.Equal(expected, result);
        }

        #endregion

        #region 边界值测试

        [Fact]
        public void Calculate_LargeNumbers_HandlesCorrectly()
        {
            // Arrange
            var large1 = double.MaxValue / 2;
            var large2 = 2;

            // Act
            var result = _calculatorService.Calculate(large1, large2, OperationType.Addition);

            // Assert
            Assert.True(double.IsFinite(result));
        }

        [Fact]
        public void Calculate_SmallNumbers_HandlesCorrectly()
        {
            // Arrange
            var small1 = double.Epsilon;
            var small2 = double.Epsilon;

            // Act
            var result = _calculatorService.Calculate(small1, small2, OperationType.Addition);

            // Assert
            Assert.True(result > 0);
        }

        #endregion
    }
}
