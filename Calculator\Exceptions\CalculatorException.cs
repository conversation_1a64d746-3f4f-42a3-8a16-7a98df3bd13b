#nullable enable

using System;

namespace Calculator.Exceptions
{

/// <summary>
/// 计算器相关的自定义异常类
/// </summary>
public class CalculatorException : Exception
{
    /// <summary>
    /// 初始化CalculatorException的新实例
    /// </summary>
    /// <param name="message">异常消息</param>
    public CalculatorException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化CalculatorException的新实例
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public CalculatorException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
}
