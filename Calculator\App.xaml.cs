﻿using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Calculator.Services;
using Calculator.ViewModels;

namespace Calculator
{
    /// <summary>
    /// WPF应用程序入口点，配置依赖注入
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider? _serviceProvider;

        /// <summary>
        /// 应用程序启动时配置依赖注入
        /// </summary>
        /// <param name="e">启动事件参数</param>
        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置依赖注入容器
            var services = new ServiceCollection();
            ConfigureServices(services);

            _serviceProvider = services.BuildServiceProvider();

            // 创建并显示主窗口
            var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }

        /// <summary>
        /// 配置服务注册
        /// </summary>
        /// <param name="services">服务集合</param>
        private void ConfigureServices(IServiceCollection services)
        {
            // 注册服务
            services.AddSingleton<ICalculatorService, CalculatorService>();

            // 注册ViewModels
            services.AddTransient<CalculatorViewModel>();

            // 注册Views
            services.AddTransient<MainWindow>();
        }

        /// <summary>
        /// 应用程序退出时清理资源
        /// </summary>
        /// <param name="e">退出事件参数</param>
        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }
}

