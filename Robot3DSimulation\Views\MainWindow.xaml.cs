using System;
using System.Windows;
using Robot3DSimulation.ViewModels;

namespace Robot3DSimulation.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="viewModel">主窗口视图模型</param>
        public MainWindow(MainWindowViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
            
            // 设置窗口关闭事件
            Closing += MainWindow_Closing;
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            if (DataContext is MainWindowViewModel viewModel)
            {
                // 如果正在运行，询问用户是否确认关闭
                if (viewModel.IsRunning)
                {
                    var result = MessageBox.Show(
                        "系统正在运行中，确定要关闭吗？",
                        "确认关闭",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.No)
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                // 执行清理操作
                try
                {
                    await viewModel.CleanupAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"清理资源时发生错误: {ex.Message}",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            }
        }
    }
}
