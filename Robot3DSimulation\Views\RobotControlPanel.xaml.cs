using System;
using System.Windows.Controls;
using Robot3DSimulation.ViewModels;

namespace Robot3DSimulation.Views
{
    /// <summary>
    /// RobotControlPanel.xaml 的交互逻辑
    /// </summary>
    public partial class RobotControlPanel : UserControl
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="viewModel">控制面板视图模型</param>
        public RobotControlPanel(RobotControlPanelViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        }
    }
}
