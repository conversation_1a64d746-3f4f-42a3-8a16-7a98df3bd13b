using System;

namespace Robot3DSimulation.Models
{
    /// <summary>
    /// ARM机械臂状态枚举
    /// </summary>
    public enum ArmStatus
    {
        /// <summary>
        /// 空闲状态
        /// </summary>
        Idle = 0,

        /// <summary>
        /// 移动中
        /// </summary>
        Moving = 1,

        /// <summary>
        /// 抓取中
        /// </summary>
        Gripping = 2,

        /// <summary>
        /// 释放中
        /// </summary>
        Releasing = 3,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error = 4,

        /// <summary>
        /// 维护模式
        /// </summary>
        Maintenance = 5
    }

    /// <summary>
    /// ARM端类型枚举
    /// </summary>
    public enum ArmEndType
    {
        /// <summary>
        /// Norse端
        /// </summary>
        Norse = 0,

        /// <summary>
        /// Smooth端
        /// </summary>
        Smooth = 1
    }

    /// <summary>
    /// ARM端信息
    /// </summary>
    public class ArmEndInfo
    {
        /// <summary>
        /// ARM端类型
        /// </summary>
        public ArmEndType EndType { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public ArmStatus Status { get; set; }

        /// <summary>
        /// 是否有晶圆
        /// </summary>
        public bool HasWafer { get; set; }

        /// <summary>
        /// 晶圆ID（如果有）
        /// </summary>
        public string? WaferId { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdate { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription => Status switch
        {
            ArmStatus.Idle => "空闲",
            ArmStatus.Moving => "移动中",
            ArmStatus.Gripping => "抓取中",
            ArmStatus.Releasing => "释放中",
            ArmStatus.Error => "错误",
            ArmStatus.Maintenance => "维护",
            _ => "未知"
        };

        /// <summary>
        /// 端类型描述
        /// </summary>
        public string EndTypeDescription => EndType switch
        {
            ArmEndType.Norse => "Norse端",
            ArmEndType.Smooth => "Smooth端",
            _ => "未知端"
        };
    }
}
