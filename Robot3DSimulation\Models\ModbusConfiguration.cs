using System;

namespace Robot3DSimulation.Models
{
    /// <summary>
    /// Modbus连接类型
    /// </summary>
    public enum ModbusConnectionType
    {
        /// <summary>
        /// TCP连接
        /// </summary>
        TCP,

        /// <summary>
        /// RTU串口连接
        /// </summary>
        RTU,

        /// <summary>
        /// ASCII串口连接
        /// </summary>
        ASCII
    }

    /// <summary>
    /// Modbus配置模型
    /// </summary>
    public class ModbusConfiguration
    {
        /// <summary>
        /// 连接类型
        /// </summary>
        public ModbusConnectionType ConnectionType { get; set; } = ModbusConnectionType.TCP;

        /// <summary>
        /// IP地址（TCP模式）
        /// </summary>
        public string IpAddress { get; set; } = "*************";

        /// <summary>
        /// 端口号（TCP模式）
        /// </summary>
        public int Port { get; set; } = 502;

        /// <summary>
        /// 串口名称（RTU/ASCII模式）
        /// </summary>
        public string SerialPort { get; set; } = "COM1";

        /// <summary>
        /// 波特率（RTU/ASCII模式）
        /// </summary>
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位（RTU/ASCII模式）
        /// </summary>
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 停止位（RTU/ASCII模式）
        /// </summary>
        public int StopBits { get; set; } = 1;

        /// <summary>
        /// 奇偶校验（RTU/ASCII模式）
        /// </summary>
        public string Parity { get; set; } = "None";

        /// <summary>
        /// 从站ID
        /// </summary>
        public byte SlaveId { get; set; } = 1;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int Timeout { get; set; } = 5000;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 重试延迟（毫秒）
        /// </summary>
        public int RetryDelay { get; set; } = 1000;

        /// <summary>
        /// 读取间隔（毫秒）
        /// </summary>
        public int ReadInterval { get; set; } = 100;

        /// <summary>
        /// 寄存器映射配置
        /// </summary>
        public ModbusRegisterMap RegisterMap { get; set; } = new();
    }

    /// <summary>
    /// Modbus寄存器映射配置
    /// </summary>
    public class ModbusRegisterMap
    {
        /// <summary>
        /// T轴当前位置寄存器地址
        /// </summary>
        public ushort TAxisCurrentPosition { get; set; } = 40001;

        /// <summary>
        /// R轴当前位置寄存器地址
        /// </summary>
        public ushort RAxisCurrentPosition { get; set; } = 40003;

        /// <summary>
        /// Z轴当前位置寄存器地址
        /// </summary>
        public ushort ZAxisCurrentPosition { get; set; } = 40005;

        /// <summary>
        /// T轴目标位置寄存器地址
        /// </summary>
        public ushort TAxisTargetPosition { get; set; } = 40007;

        /// <summary>
        /// R轴目标位置寄存器地址
        /// </summary>
        public ushort RAxisTargetPosition { get; set; } = 40009;

        /// <summary>
        /// Z轴目标位置寄存器地址
        /// </summary>
        public ushort ZAxisTargetPosition { get; set; } = 40011;

        // 兼容性属性
        public RegisterDefinition TAxisPosition => new() { Address = TAxisCurrentPosition, DataType = ModbusDataType.Float32, Scale = 1.0 };
        public RegisterDefinition RAxisPosition => new() { Address = RAxisCurrentPosition, DataType = ModbusDataType.Float32, Scale = 1.0 };
        public RegisterDefinition ZAxisPosition => new() { Address = ZAxisCurrentPosition, DataType = ModbusDataType.Float32, Scale = 1.0 };
        public RegisterDefinition TAxisTarget => new() { Address = TAxisTargetPosition, DataType = ModbusDataType.Float32, Scale = 1.0 };
        public RegisterDefinition RAxisTarget => new() { Address = RAxisTargetPosition, DataType = ModbusDataType.Float32, Scale = 1.0 };
        public RegisterDefinition ZAxisTarget => new() { Address = ZAxisTargetPosition, DataType = ModbusDataType.Float32, Scale = 1.0 };

        /// <summary>
        /// Norse端状态寄存器地址
        /// </summary>
        public ushort NorseEndStatus { get; set; } = 40013;

        /// <summary>
        /// Smooth端状态寄存器地址
        /// </summary>
        public ushort SmoothEndStatus { get; set; } = 40014;

        // 兼容性属性
        public RegisterDefinition NorseArmStatus => new() { Address = NorseEndStatus, DataType = ModbusDataType.Int16, Scale = 1.0 };
        public RegisterDefinition SmoothArmStatus => new() { Address = SmoothEndStatus, DataType = ModbusDataType.Int16, Scale = 1.0 };

        /// <summary>
        /// 系统状态字寄存器地址
        /// </summary>
        public ushort SystemStatus { get; set; } = 40015;

        /// <summary>
        /// 控制字寄存器地址
        /// </summary>
        public ushort ControlWord { get; set; } = 40016;

        /// <summary>
        /// 错误代码寄存器地址
        /// </summary>
        public ushort ErrorCode { get; set; } = 40017;

        /// <summary>
        /// 运行模式寄存器地址
        /// </summary>
        public ushort RunMode { get; set; } = 40018;

        /// <summary>
        /// 速度设置寄存器地址
        /// </summary>
        public ushort VelocitySet { get; set; } = 40019;

        /// <summary>
        /// 保留寄存器起始地址
        /// </summary>
        public ushort ReservedStart { get; set; } = 40020;

        /// <summary>
        /// 急停寄存器地址
        /// </summary>
        public ushort EmergencyStopAddress { get; set; } = 40021;

        // 兼容性属性
        public RegisterDefinition EmergencyStop => new() { Address = EmergencyStopAddress, DataType = ModbusDataType.Boolean, Scale = 1.0 };
    }

    /// <summary>
    /// Modbus数据类型
    /// </summary>
    public enum ModbusDataType
    {
        /// <summary>
        /// 16位整数
        /// </summary>
        Int16,

        /// <summary>
        /// 32位整数
        /// </summary>
        Int32,

        /// <summary>
        /// 32位浮点数
        /// </summary>
        Float32,

        /// <summary>
        /// 布尔值
        /// </summary>
        Boolean
    }

    /// <summary>
    /// 寄存器定义
    /// </summary>
    public class RegisterDefinition
    {
        /// <summary>
        /// 寄存器地址
        /// </summary>
        public ushort Address { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public ModbusDataType DataType { get; set; }

        /// <summary>
        /// 寄存器数量
        /// </summary>
        public ushort Count { get; set; } = 1;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadOnly { get; set; } = true;

        /// <summary>
        /// 缩放因子
        /// </summary>
        public double ScaleFactor { get; set; } = 1.0;

        /// <summary>
        /// 偏移量
        /// </summary>
        public double Offset { get; set; } = 0.0;

        /// <summary>
        /// 缩放（兼容性属性）
        /// </summary>
        public double Scale
        {
            get => ScaleFactor;
            set => ScaleFactor = value;
        }
    }
}
