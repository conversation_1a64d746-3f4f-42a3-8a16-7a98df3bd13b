#nullable enable

using CommunityToolkit.Mvvm.ComponentModel;
using Calculator.Enums;

namespace Calculator.Models
{

/// <summary>
/// 计算器状态模型，使用CommunityToolkit.Mvvm的Source Generators
/// </summary>
public partial class CalculatorModel : ObservableObject
{
    /// <summary>
    /// 第一个操作数
    /// </summary>
    [ObservableProperty]
    private double firstOperand;

    /// <summary>
    /// 第二个操作数
    /// </summary>
    [ObservableProperty]
    private double secondOperand;

    /// <summary>
    /// 当前操作类型
    /// </summary>
    [ObservableProperty]
    private OperationType? currentOperation;

    /// <summary>
    /// 是否有待处理的操作
    /// </summary>
    [ObservableProperty]
    private bool isOperationPending;

    /// <summary>
    /// 错误消息
    /// </summary>
    [ObservableProperty]
    private string? errorMessage;

    /// <summary>
    /// 是否处于错误状态
    /// </summary>
    [ObservableProperty]
    private bool hasError;

    /// <summary>
    /// 是否等待新的操作数输入
    /// </summary>
    [ObservableProperty]
    private bool waitingForOperand;

    /// <summary>
    /// 重置计算器状态
    /// </summary>
    public void Reset()
    {
        FirstOperand = 0;
        SecondOperand = 0;
        CurrentOperation = null;
        IsOperationPending = false;
        ErrorMessage = null;
        HasError = false;
        WaitingForOperand = false;
    }

    /// <summary>
    /// 设置错误状态
    /// </summary>
    /// <param name="message">错误消息</param>
    public void SetError(string message)
    {
        ErrorMessage = message;
        HasError = true;
    }

    /// <summary>
    /// 清除错误状态
    /// </summary>
    public void ClearError()
    {
        ErrorMessage = null;
        HasError = false;
    }
}
}
