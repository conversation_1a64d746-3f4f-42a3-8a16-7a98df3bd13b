using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Robot3DSimulation.Models;

namespace Robot3DSimulation.Services
{
    /// <summary>
    /// 配置管理服务实现
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        private readonly ILogger<ConfigurationService> _logger;
        private RobotConfiguration _robotConfig;
        private ModbusConfiguration _modbusConfig;
        private RenderingConfiguration _renderingConfig;
        private DebugConfiguration _debugConfig;

        /// <summary>
        /// 机器人配置
        /// </summary>
        public RobotConfiguration RobotConfig => _robotConfig;

        /// <summary>
        /// Modbus配置
        /// </summary>
        public ModbusConfiguration ModbusConfig => _modbusConfig;

        /// <summary>
        /// 渲染配置
        /// </summary>
        public RenderingConfiguration RenderingConfig => _renderingConfig;

        /// <summary>
        /// 调试配置
        /// </summary>
        public DebugConfiguration DebugConfig => _debugConfig;

        /// <summary>
        /// 配置变化事件
        /// </summary>
        public event EventHandler<string>? ConfigurationChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public ConfigurationService(ILogger<ConfigurationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // 初始化默认配置
            InitializeDefaultConfiguration();
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        /// <returns>加载是否成功</returns>
        public async Task<bool> LoadConfigurationAsync(string configFilePath = "appsettings.json")
        {
            try
            {
                if (!File.Exists(configFilePath))
                {
                    _logger.LogWarning("配置文件不存在: {FilePath}，使用默认配置", configFilePath);
                    return await SaveConfigurationAsync(configFilePath);
                }

                var jsonContent = await File.ReadAllTextAsync(configFilePath);
                var configData = JsonSerializer.Deserialize<ConfigurationData>(jsonContent, GetJsonOptions());

                if (configData != null)
                {
                    _robotConfig = configData.Robot ?? CreateDefaultRobotConfiguration();
                    _modbusConfig = configData.Modbus ?? CreateDefaultModbusConfiguration();
                    _renderingConfig = configData.Rendering ?? CreateDefaultRenderingConfiguration();
                    _debugConfig = configData.Debug ?? CreateDefaultDebugConfiguration();

                    _logger.LogInformation("配置文件加载成功: {FilePath}", configFilePath);
                    ConfigurationChanged?.Invoke(this, "配置已加载");
                    return true;
                }

                _logger.LogError("配置文件格式错误: {FilePath}", configFilePath);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载配置文件时发生错误: {FilePath}", configFilePath);
                return false;
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveConfigurationAsync(string configFilePath = "appsettings.json")
        {
            try
            {
                var configData = new ConfigurationData
                {
                    Robot = _robotConfig,
                    Modbus = _modbusConfig,
                    Rendering = _renderingConfig,
                    Debug = _debugConfig
                };

                var jsonContent = JsonSerializer.Serialize(configData, GetJsonOptions());
                await File.WriteAllTextAsync(configFilePath, jsonContent);

                _logger.LogInformation("配置文件保存成功: {FilePath}", configFilePath);
                ConfigurationChanged?.Invoke(this, "配置已保存");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置文件时发生错误: {FilePath}", configFilePath);
                return false;
            }
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefault()
        {
            InitializeDefaultConfiguration();
            _logger.LogInformation("配置已重置为默认值");
            ConfigurationChanged?.Invoke(this, "配置已重置");
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果和错误信息</returns>
        public (bool IsValid, string ErrorMessage) ValidateConfiguration()
        {
            try
            {
                // 验证机器人配置
                if (_robotConfig.TAxis.MaxLimit <= _robotConfig.TAxis.MinLimit)
                    return (false, "T轴最大限制必须大于最小限制");

                if (_robotConfig.RAxis.MaxLimit <= _robotConfig.RAxis.MinLimit)
                    return (false, "R轴最大限制必须大于最小限制");

                if (_robotConfig.ZAxis.MaxLimit <= _robotConfig.ZAxis.MinLimit)
                    return (false, "Z轴最大限制必须大于最小限制");

                // 验证Modbus配置
                if (_modbusConfig.ConnectionType == ModbusConnectionType.TCP)
                {
                    if (string.IsNullOrEmpty(_modbusConfig.IpAddress))
                        return (false, "TCP模式下IP地址不能为空");

                    if (_modbusConfig.Port <= 0 || _modbusConfig.Port > 65535)
                        return (false, "TCP端口必须在1-65535范围内");
                }
                else
                {
                    if (string.IsNullOrEmpty(_modbusConfig.SerialPort))
                        return (false, "串口模式下串口名称不能为空");

                    if (_modbusConfig.BaudRate <= 0)
                        return (false, "波特率必须大于0");
                }

                // 验证渲染配置
                if (_renderingConfig.TargetFrameRate <= 0)
                    return (false, "目标帧率必须大于0");

                if (_renderingConfig.MaxRenderDistance <= 0)
                    return (false, "最大渲染距离必须大于0");

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"验证配置时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新机器人配置
        /// </summary>
        /// <param name="config">新的机器人配置</param>
        public void UpdateRobotConfiguration(RobotConfiguration config)
        {
            _robotConfig = config ?? throw new ArgumentNullException(nameof(config));
            _logger.LogInformation("机器人配置已更新");
            ConfigurationChanged?.Invoke(this, "机器人配置已更新");
        }

        /// <summary>
        /// 更新Modbus配置
        /// </summary>
        /// <param name="config">新的Modbus配置</param>
        public void UpdateModbusConfiguration(ModbusConfiguration config)
        {
            _modbusConfig = config ?? throw new ArgumentNullException(nameof(config));
            _logger.LogInformation("Modbus配置已更新");
            ConfigurationChanged?.Invoke(this, "Modbus配置已更新");
        }

        /// <summary>
        /// 更新渲染配置
        /// </summary>
        /// <param name="config">新的渲染配置</param>
        public void UpdateRenderingConfiguration(RenderingConfiguration config)
        {
            _renderingConfig = config ?? throw new ArgumentNullException(nameof(config));
            _logger.LogInformation("渲染配置已更新");
            ConfigurationChanged?.Invoke(this, "渲染配置已更新");
        }

        /// <summary>
        /// 更新调试配置
        /// </summary>
        /// <param name="config">新的调试配置</param>
        public void UpdateDebugConfiguration(DebugConfiguration config)
        {
            _debugConfig = config ?? throw new ArgumentNullException(nameof(config));
            _logger.LogInformation("调试配置已更新");
            ConfigurationChanged?.Invoke(this, "调试配置已更新");
        }

        /// <summary>
        /// 导出配置到文件
        /// </summary>
        /// <param name="filePath">导出文件路径</param>
        /// <returns>导出是否成功</returns>
        public async Task<bool> ExportConfigurationAsync(string filePath)
        {
            try
            {
                return await SaveConfigurationAsync(filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出配置时发生错误: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 从文件导入配置
        /// </summary>
        /// <param name="filePath">导入文件路径</param>
        /// <returns>导入是否成功</returns>
        public async Task<bool> ImportConfigurationAsync(string filePath)
        {
            try
            {
                return await LoadConfigurationAsync(filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入配置时发生错误: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 获取配置摘要信息
        /// </summary>
        /// <returns>配置摘要</returns>
        public string GetConfigurationSummary()
        {
            return $"机器人配置: T轴({_robotConfig.TAxis.MinLimit}-{_robotConfig.TAxis.MaxLimit}), " +
                   $"R轴({_robotConfig.RAxis.MinLimit}-{_robotConfig.RAxis.MaxLimit}), " +
                   $"Z轴({_robotConfig.ZAxis.MinLimit}-{_robotConfig.ZAxis.MaxLimit}); " +
                   $"Modbus: {_modbusConfig.ConnectionType}; " +
                   $"渲染: {_renderingConfig.Quality}质量";
        }

        /// <summary>
        /// 备份当前配置
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>备份是否成功</returns>
        public async Task<bool> BackupConfigurationAsync(string backupName)
        {
            try
            {
                var backupDir = "ConfigBackups";
                if (!Directory.Exists(backupDir))
                    Directory.CreateDirectory(backupDir);

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupPath = Path.Combine(backupDir, $"{backupName}_{timestamp}.json");
                
                return await SaveConfigurationAsync(backupPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "备份配置时发生错误: {BackupName}", backupName);
                return false;
            }
        }

        /// <summary>
        /// 恢复配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>恢复是否成功</returns>
        public async Task<bool> RestoreConfigurationAsync(string backupName)
        {
            try
            {
                var backupDir = "ConfigBackups";
                var backupFiles = Directory.GetFiles(backupDir, $"{backupName}_*.json");
                
                if (backupFiles.Length == 0)
                {
                    _logger.LogWarning("未找到备份文件: {BackupName}", backupName);
                    return false;
                }

                // 使用最新的备份文件
                Array.Sort(backupFiles);
                var latestBackup = backupFiles[^1];
                
                return await LoadConfigurationAsync(latestBackup);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复配置备份时发生错误: {BackupName}", backupName);
                return false;
            }
        }

        /// <summary>
        /// 获取可用的配置备份列表
        /// </summary>
        /// <returns>备份名称列表</returns>
        public async Task<string[]> GetAvailableBackupsAsync()
        {
            try
            {
                var backupDir = "ConfigBackups";
                if (!Directory.Exists(backupDir))
                    return Array.Empty<string>();

                var backupFiles = Directory.GetFiles(backupDir, "*.json");
                var backupNames = new string[backupFiles.Length];

                for (int i = 0; i < backupFiles.Length; i++)
                {
                    var fileName = Path.GetFileNameWithoutExtension(backupFiles[i]);
                    var lastUnderscoreIndex = fileName.LastIndexOf('_');
                    backupNames[i] = lastUnderscoreIndex > 0 ? fileName[..lastUnderscoreIndex] : fileName;
                }

                return await Task.FromResult(backupNames);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取备份列表时发生错误");
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        private void InitializeDefaultConfiguration()
        {
            _robotConfig = CreateDefaultRobotConfiguration();
            _modbusConfig = CreateDefaultModbusConfiguration();
            _renderingConfig = CreateDefaultRenderingConfiguration();
            _debugConfig = CreateDefaultDebugConfiguration();
        }

        /// <summary>
        /// 创建默认机器人配置
        /// </summary>
        /// <returns>默认机器人配置</returns>
        private static RobotConfiguration CreateDefaultRobotConfiguration()
        {
            return new RobotConfiguration
            {
                TAxis = new AxisConfiguration
                {
                    Name = "T轴",
                    Min = 0,
                    Max = 1000,
                    SafetyMargin = 10,
                    MaxVelocity = 100,
                    MaxAcceleration = 50
                },
                RAxis = new AxisConfiguration
                {
                    Name = "R轴",
                    Min = -180,
                    Max = 180,
                    SafetyMargin = 5,
                    MaxVelocity = 90,
                    MaxAcceleration = 45
                },
                ZAxis = new AxisConfiguration
                {
                    Name = "Z轴",
                    Min = 0,
                    Max = 200,
                    SafetyMargin = 5,
                    MaxVelocity = 50,
                    MaxAcceleration = 25
                },
                UpdateFrequency = 10,
                TrajectoryBufferSize = 1000
            };
        }

        /// <summary>
        /// 创建默认Modbus配置
        /// </summary>
        /// <returns>默认Modbus配置</returns>
        private static ModbusConfiguration CreateDefaultModbusConfiguration()
        {
            return new ModbusConfiguration
            {
                ConnectionType = ModbusConnectionType.TCP,
                IpAddress = "*************",
                Port = 502,
                SlaveId = 1,
                Timeout = 5000,
                SerialPort = "COM1",
                BaudRate = 9600,
                DataBits = 8,
                Parity = "None",
                StopBits = 1,
                RegisterMap = new ModbusRegisterMap
                {
                    TAxisPosition = new RegisterDefinition { Address = 40001, DataType = "Float32", Scale = 1.0 },
                    RAxisPosition = new RegisterDefinition { Address = 40003, DataType = "Float32", Scale = 1.0 },
                    ZAxisPosition = new RegisterDefinition { Address = 40005, DataType = "Float32", Scale = 1.0 },
                    TAxisTarget = new RegisterDefinition { Address = 40011, DataType = "Float32", Scale = 1.0 },
                    RAxisTarget = new RegisterDefinition { Address = 40013, DataType = "Float32", Scale = 1.0 },
                    ZAxisTarget = new RegisterDefinition { Address = 40015, DataType = "Float32", Scale = 1.0 },
                    NorseArmStatus = new RegisterDefinition { Address = 40021, DataType = "Int16", Scale = 1.0 },
                    SmoothArmStatus = new RegisterDefinition { Address = 40022, DataType = "Int16", Scale = 1.0 },
                    SystemStatus = new RegisterDefinition { Address = 40031, DataType = "Int16", Scale = 1.0 },
                    EmergencyStop = new RegisterDefinition { Address = 40032, DataType = "Boolean", Scale = 1.0 }
                }
            };
        }

        /// <summary>
        /// 创建默认渲染配置
        /// </summary>
        /// <returns>默认渲染配置</returns>
        private static RenderingConfiguration CreateDefaultRenderingConfiguration()
        {
            return new RenderingConfiguration
            {
                AutoQuality = true,
                TargetFrameRate = 60,
                MaxRenderDistance = 2000.0,
                EnableAntiAliasing = true,
                EnableShadows = true,
                LightIntensity = 1.0,
                BackgroundColor = "#F5F5F5"
            };
        }

        /// <summary>
        /// 创建默认调试配置
        /// </summary>
        /// <returns>默认调试配置</returns>
        private static DebugConfiguration CreateDefaultDebugConfiguration()
        {
            return new DebugConfiguration
            {
                EnableDebugPanel = true,
                LogToFile = true,
                LogFilePath = "logs/robot3d.log",
                MaxLogFileSize = 10 * 1024 * 1024, // 10MB
                EnablePerformanceMonitoring = true,
                PerformanceDataRetentionMinutes = 60
            };
        }

        /// <summary>
        /// 获取JSON序列化选项
        /// </summary>
        /// <returns>JSON序列化选项</returns>
        private static JsonSerializerOptions GetJsonOptions()
        {
            return new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
        }

        /// <summary>
        /// 配置数据容器类
        /// </summary>
        private class ConfigurationData
        {
            public RobotConfiguration? Robot { get; set; }
            public ModbusConfiguration? Modbus { get; set; }
            public RenderingConfiguration? Rendering { get; set; }
            public DebugConfiguration? Debug { get; set; }
        }
    }
}
