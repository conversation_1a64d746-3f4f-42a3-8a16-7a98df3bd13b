{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Modbus": {"ConnectionType": "TCP", "IpAddress": "*************", "Port": 502, "SlaveId": 1, "Timeout": 5000, "RetryCount": 3, "RetryDelay": 1000, "ReadInterval": 100}, "Robot": {"TAxis": {"Min": -1000.0, "Max": 1000.0, "Unit": "mm", "SafetyMargin": 50.0}, "RAxis": {"Min": -360.0, "Max": 360.0, "Unit": "degree", "SafetyMargin": 10.0}, "ZAxis": {"Min": 0.0, "Max": 500.0, "Unit": "mm", "SafetyMargin": 25.0}, "UpdateFrequency": 100, "MaxTrajectoryPoints": 1000, "EnableSafetyCheck": true}, "Rendering": {"AutoQuality": true, "TargetFrameRate": 30, "MaxRenderDistance": 2000.0, "EnableAntiAliasing": true, "EnableShadows": true, "LightIntensity": 1.0}, "Debug": {"EnableDebugPanel": true, "LogToFile": true, "LogFilePath": "logs/robot3d.log", "MaxLogFileSize": 10485760, "EnablePerformanceMonitoring": true}}