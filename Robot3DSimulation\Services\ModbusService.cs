using System;
using System.IO.Ports;
using System.Net.Sockets;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using NModbus;
using Robot3DSimulation.Models;

namespace Robot3DSimulation.Services
{
    /// <summary>
    /// Modbus通信服务实现
    /// </summary>
    public class ModbusService : IModbusService
    {
        private readonly ILogger<ModbusService> _logger;
        private ModbusConfiguration? _config;
        private IModbusMaster? _modbusMaster;
        private TcpClient? _tcpClient;
        private SerialPort? _serialPort;
        private bool _disposed;

        /// <summary>
        /// 连接状态
        /// </summary>
        public bool IsConnected { get; private set; }

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event EventHandler<bool>? ConnectionStatusChanged;

        /// <summary>
        /// 通信错误事件
        /// </summary>
        public event EventHandler<string>? CommunicationError;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public ModbusService(ILogger<ModbusService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 连接到Modbus设备
        /// </summary>
        /// <param name="config">Modbus配置</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectAsync(ModbusConfiguration config)
        {
            try
            {
                _config = config ?? throw new ArgumentNullException(nameof(config));
                
                _logger.LogInformation("正在连接到Modbus设备，类型: {ConnectionType}", config.ConnectionType);

                // 先断开现有连接
                await DisconnectAsync();

                bool connected = config.ConnectionType switch
                {
                    ModbusConnectionType.TCP => await ConnectTcpAsync(),
                    ModbusConnectionType.RTU => await ConnectRtuAsync(),
                    ModbusConnectionType.ASCII => await ConnectAsciiAsync(),
                    _ => throw new NotSupportedException($"不支持的连接类型: {config.ConnectionType}")
                };

                IsConnected = connected;
                ConnectionStatusChanged?.Invoke(this, connected);

                if (connected)
                {
                    _logger.LogInformation("Modbus连接成功");
                }
                else
                {
                    _logger.LogError("Modbus连接失败");
                }

                return connected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接Modbus设备时发生错误");
                CommunicationError?.Invoke(this, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 连接TCP模式
        /// </summary>
        /// <returns>连接是否成功</returns>
        private async Task<bool> ConnectTcpAsync()
        {
            if (_config == null) return false;

            try
            {
                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(_config.IpAddress, _config.Port);
                
                var factory = new ModbusFactory();
                _modbusMaster = factory.CreateMaster(_tcpClient);
                // 注意：NModbus 3.x 中超时设置可能需要在其他地方配置

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TCP连接失败: {IpAddress}:{Port}", _config.IpAddress, _config.Port);
                _tcpClient?.Close();
                _tcpClient = null;
                return false;
            }
        }

        /// <summary>
        /// 连接RTU模式
        /// </summary>
        /// <returns>连接是否成功</returns>
        private async Task<bool> ConnectRtuAsync()
        {
            if (_config == null) return false;

            try
            {
                _serialPort = new SerialPort(_config.SerialPort, _config.BaudRate, 
                    GetParity(_config.Parity), _config.DataBits, GetStopBits(_config.StopBits));
                
                _serialPort.Open();
                
                var factory = new ModbusFactory();
                _modbusMaster = factory.CreateRtuMaster(_serialPort);
                // 注意：NModbus 3.x 中超时设置可能需要在其他地方配置

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RTU连接失败: {SerialPort}", _config.SerialPort);
                _serialPort?.Close();
                _serialPort = null;
                return false;
            }
        }

        /// <summary>
        /// 连接ASCII模式
        /// </summary>
        /// <returns>连接是否成功</returns>
        private async Task<bool> ConnectAsciiAsync()
        {
            if (_config == null) return false;

            try
            {
                _serialPort = new SerialPort(_config.SerialPort, _config.BaudRate, 
                    GetParity(_config.Parity), _config.DataBits, GetStopBits(_config.StopBits));
                
                _serialPort.Open();
                
                var factory = new ModbusFactory();
                _modbusMaster = factory.CreateAsciiMaster(_serialPort);
                // 注意：NModbus 3.x 中超时设置可能需要在其他地方配置

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ASCII连接失败: {SerialPort}", _config.SerialPort);
                _serialPort?.Close();
                _serialPort = null;
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns>断开任务</returns>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_modbusMaster != null)
                {
                    _modbusMaster.Dispose();
                    _modbusMaster = null;
                }

                if (_tcpClient != null)
                {
                    _tcpClient.Close();
                    _tcpClient = null;
                }

                if (_serialPort != null)
                {
                    _serialPort.Close();
                    _serialPort = null;
                }

                if (IsConnected)
                {
                    IsConnected = false;
                    ConnectionStatusChanged?.Invoke(this, false);
                    _logger.LogInformation("Modbus连接已断开");
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开Modbus连接时发生错误");
            }
        }

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="count">寄存器数量</param>
        /// <returns>寄存器值数组</returns>
        public async Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count)
        {
            if (!IsConnected || _modbusMaster == null || _config == null)
                throw new InvalidOperationException("Modbus未连接");

            try
            {
                var result = await _modbusMaster.ReadHoldingRegistersAsync(_config.SlaveId, startAddress, count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取保持寄存器失败: 地址={StartAddress}, 数量={Count}", startAddress, count);
                CommunicationError?.Invoke(this, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 写入保持寄存器
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">要写入的值</param>
        /// <returns>写入是否成功</returns>
        public async Task<bool> WriteHoldingRegistersAsync(ushort startAddress, ushort[] values)
        {
            if (!IsConnected || _modbusMaster == null || _config == null)
                return false;

            try
            {
                await _modbusMaster.WriteMultipleRegistersAsync(_config.SlaveId, startAddress, values);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "写入保持寄存器失败: 地址={StartAddress}, 数量={Count}", startAddress, values.Length);
                CommunicationError?.Invoke(this, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 读取单个保持寄存器
        /// </summary>
        /// <param name="address">寄存器地址</param>
        /// <returns>寄存器值</returns>
        public async Task<ushort> ReadHoldingRegisterAsync(ushort address)
        {
            var result = await ReadHoldingRegistersAsync(address, 1);
            return result[0];
        }

        /// <summary>
        /// 写入单个保持寄存器
        /// </summary>
        /// <param name="address">寄存器地址</param>
        /// <param name="value">要写入的值</param>
        /// <returns>写入是否成功</returns>
        public async Task<bool> WriteHoldingRegisterAsync(ushort address, ushort value)
        {
            if (!IsConnected || _modbusMaster == null || _config == null)
                return false;

            try
            {
                await _modbusMaster.WriteSingleRegisterAsync(_config.SlaveId, address, value);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "写入单个寄存器失败: 地址={Address}, 值={Value}", address, value);
                CommunicationError?.Invoke(this, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 读取32位浮点数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <returns>浮点数值</returns>
        public async Task<float> ReadFloat32Async(ushort startAddress)
        {
            var registers = await ReadHoldingRegistersAsync(startAddress, 2);
            var bytes = new byte[4];
            
            // 高位在前，低位在后
            bytes[0] = (byte)(registers[1] & 0xFF);
            bytes[1] = (byte)((registers[1] >> 8) & 0xFF);
            bytes[2] = (byte)(registers[0] & 0xFF);
            bytes[3] = (byte)((registers[0] >> 8) & 0xFF);
            
            return BitConverter.ToSingle(bytes, 0);
        }

        /// <summary>
        /// 写入32位浮点数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="value">要写入的浮点数</param>
        /// <returns>写入是否成功</returns>
        public async Task<bool> WriteFloat32Async(ushort startAddress, float value)
        {
            var bytes = BitConverter.GetBytes(value);
            var registers = new ushort[2];

            // 高位在前，低位在后
            registers[0] = (ushort)((bytes[3] << 8) | bytes[2]);
            registers[1] = (ushort)((bytes[1] << 8) | bytes[0]);

            return await WriteHoldingRegistersAsync(startAddress, registers);
        }

        /// <summary>
        /// 读取32位整数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <returns>整数值</returns>
        public async Task<int> ReadInt32Async(ushort startAddress)
        {
            var registers = await ReadHoldingRegistersAsync(startAddress, 2);
            return (registers[0] << 16) | registers[1];
        }

        /// <summary>
        /// 写入32位整数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="value">要写入的整数</param>
        /// <returns>写入是否成功</returns>
        public async Task<bool> WriteInt32Async(ushort startAddress, int value)
        {
            var registers = new ushort[2];
            registers[0] = (ushort)((value >> 16) & 0xFFFF);
            registers[1] = (ushort)(value & 0xFFFF);

            return await WriteHoldingRegistersAsync(startAddress, registers);
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <returns>连接是否正常</returns>
        public async Task<bool> TestConnectionAsync()
        {
            if (!IsConnected)
                return false;

            try
            {
                // 尝试读取一个寄存器来测试连接
                await ReadHoldingRegisterAsync(40001);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取连接信息
        /// </summary>
        /// <returns>连接信息字符串</returns>
        public string GetConnectionInfo()
        {
            if (_config == null)
                return "未配置";

            return _config.ConnectionType switch
            {
                ModbusConnectionType.TCP => $"TCP: {_config.IpAddress}:{_config.Port}",
                ModbusConnectionType.RTU => $"RTU: {_config.SerialPort} ({_config.BaudRate})",
                ModbusConnectionType.ASCII => $"ASCII: {_config.SerialPort} ({_config.BaudRate})",
                _ => "未知连接类型"
            };
        }

        /// <summary>
        /// 获取奇偶校验设置
        /// </summary>
        /// <param name="parity">奇偶校验字符串</param>
        /// <returns>奇偶校验枚举</returns>
        private static Parity GetParity(string parity)
        {
            return parity.ToUpper() switch
            {
                "NONE" => Parity.None,
                "ODD" => Parity.Odd,
                "EVEN" => Parity.Even,
                "MARK" => Parity.Mark,
                "SPACE" => Parity.Space,
                _ => Parity.None
            };
        }

        /// <summary>
        /// 获取停止位设置
        /// </summary>
        /// <param name="stopBits">停止位数量</param>
        /// <returns>停止位枚举</returns>
        private static StopBits GetStopBits(int stopBits)
        {
            return stopBits switch
            {
                1 => StopBits.One,
                2 => StopBits.Two,
                _ => StopBits.One
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait();
                _disposed = true;
            }
        }
    }
}
