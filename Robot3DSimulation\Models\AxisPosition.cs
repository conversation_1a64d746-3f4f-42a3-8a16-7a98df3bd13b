using System;
using CommunityToolkit.Mvvm.ComponentModel;

namespace Robot3DSimulation.Models
{
    /// <summary>
    /// 轴位置数据模型
    /// </summary>
    public partial class AxisPosition : ObservableObject
    {
        /// <summary>
        /// 轴名称
        /// </summary>
        [ObservableProperty]
        private string axisName = string.Empty;

        /// <summary>
        /// 当前位置
        /// </summary>
        [ObservableProperty]
        private double currentPosition;

        /// <summary>
        /// 目标位置
        /// </summary>
        [ObservableProperty]
        private double targetPosition;

        /// <summary>
        /// 最小限位
        /// </summary>
        [ObservableProperty]
        private double minLimit;

        /// <summary>
        /// 最大限位
        /// </summary>
        [ObservableProperty]
        private double maxLimit;

        /// <summary>
        /// 安全余量
        /// </summary>
        [ObservableProperty]
        private double safetyMargin;

        /// <summary>
        /// 单位
        /// </summary>
        [ObservableProperty]
        private string unit = string.Empty;

        /// <summary>
        /// 是否在安全范围内
        /// </summary>
        [ObservableProperty]
        private bool isInSafeRange = true;

        /// <summary>
        /// 是否正在移动
        /// </summary>
        [ObservableProperty]
        private bool isMoving;

        /// <summary>
        /// 移动速度
        /// </summary>
        [ObservableProperty]
        private double velocity;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [ObservableProperty]
        private DateTime lastUpdate;

        /// <summary>
        /// 错误信息
        /// </summary>
        [ObservableProperty]
        private string? errorMessage;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AxisPosition()
        {
            LastUpdate = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="minLimit">最小限位</param>
        /// <param name="maxLimit">最大限位</param>
        /// <param name="unit">单位</param>
        /// <param name="safetyMargin">安全余量</param>
        public AxisPosition(string axisName, double minLimit, double maxLimit, string unit, double safetyMargin = 0)
        {
            AxisName = axisName;
            MinLimit = minLimit;
            MaxLimit = maxLimit;
            Unit = unit;
            SafetyMargin = safetyMargin;
            LastUpdate = DateTime.Now;
        }

        /// <summary>
        /// 更新当前位置
        /// </summary>
        /// <param name="position">新位置</param>
        public void UpdatePosition(double position)
        {
            CurrentPosition = position;
            LastUpdate = DateTime.Now;
            CheckSafetyRange();
        }

        /// <summary>
        /// 设置目标位置
        /// </summary>
        /// <param name="position">目标位置</param>
        /// <returns>是否设置成功</returns>
        public bool SetTargetPosition(double position)
        {
            if (IsPositionValid(position))
            {
                TargetPosition = position;
                ErrorMessage = null;
                return true;
            }
            
            ErrorMessage = $"目标位置 {position} {Unit} 超出安全范围 [{MinLimit + SafetyMargin}, {MaxLimit - SafetyMargin}] {Unit}";
            return false;
        }

        /// <summary>
        /// 检查位置是否有效
        /// </summary>
        /// <param name="position">位置值</param>
        /// <returns>是否有效</returns>
        public bool IsPositionValid(double position)
        {
            return position >= (MinLimit + SafetyMargin) && position <= (MaxLimit - SafetyMargin);
        }

        /// <summary>
        /// 检查安全范围
        /// </summary>
        private void CheckSafetyRange()
        {
            IsInSafeRange = IsPositionValid(CurrentPosition);
            
            if (!IsInSafeRange)
            {
                ErrorMessage = $"当前位置 {CurrentPosition} {Unit} 超出安全范围";
            }
            else
            {
                ErrorMessage = null;
            }
        }

        /// <summary>
        /// 获取位置百分比（相对于限位范围）
        /// </summary>
        /// <returns>位置百分比 (0-100)</returns>
        public double GetPositionPercentage()
        {
            if (Math.Abs(MaxLimit - MinLimit) < double.Epsilon)
                return 0;
                
            return ((CurrentPosition - MinLimit) / (MaxLimit - MinLimit)) * 100;
        }

        /// <summary>
        /// 获取到目标位置的距离
        /// </summary>
        /// <returns>距离</returns>
        public double GetDistanceToTarget()
        {
            return Math.Abs(TargetPosition - CurrentPosition);
        }

        /// <summary>
        /// 是否到达目标位置
        /// </summary>
        /// <param name="tolerance">容差</param>
        /// <returns>是否到达</returns>
        public bool IsAtTarget(double tolerance = 0.1)
        {
            return GetDistanceToTarget() <= tolerance;
        }

        /// <summary>
        /// 设置目标位置（兼容性方法）
        /// </summary>
        /// <param name="target">目标位置</param>
        public void SetTarget(double target)
        {
            SetTargetPosition(target);
        }

        /// <summary>
        /// 设置限位（兼容性方法）
        /// </summary>
        /// <param name="min">最小限位</param>
        /// <param name="max">最大限位</param>
        public void SetLimits(double min, double max)
        {
            MinLimit = min;
            MaxLimit = max;
            CheckSafetyRange();
        }
    }
}
