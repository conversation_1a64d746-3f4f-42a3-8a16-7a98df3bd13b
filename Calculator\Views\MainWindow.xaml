﻿<Window x:Class="Calculator.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Calculator"
        mc:Ignorable="d"
        Title="计算器" Height="500" Width="350"
        MinHeight="500" MinWidth="350"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 按钮样式 -->
        <Style x:Key="CalculatorButtonStyle" TargetType="Button">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D0D0D0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 数字按钮样式 -->
        <Style x:Key="NumberButtonStyle" TargetType="Button" BasedOn="{StaticResource CalculatorButtonStyle}">
            <Setter Property="Background" Value="#FFFFFF"/>
        </Style>

        <!-- 操作按钮样式 -->
        <Style x:Key="OperatorButtonStyle" TargetType="Button" BasedOn="{StaticResource CalculatorButtonStyle}">
            <Setter Property="Background" Value="#FF9500"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- 功能按钮样式 -->
        <Style x:Key="FunctionButtonStyle" TargetType="Button" BasedOn="{StaticResource CalculatorButtonStyle}">
            <Setter Property="Background" Value="#A6A6A6"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- 显示屏样式 -->
        <Style x:Key="DisplayStyle" TargetType="TextBox">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="Background" Value="Black"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="10"/>
        </Style>
    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 显示屏 -->
        <TextBox Grid.Row="0"
                 Text="{Binding Display, Mode=OneWay}"
                 Style="{StaticResource DisplayStyle}"/>

        <!-- 错误消息显示 -->
        <TextBlock Grid.Row="0"
                   Text="{Binding ErrorMessage}"
                   Foreground="Red"
                   FontSize="12"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Bottom"
                   Margin="0,0,0,5"
                   Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>

        <!-- 按钮网格 -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 第一行：清除、退格、除法 -->
            <Button Grid.Row="0" Grid.Column="0" Content="C"
                    Style="{StaticResource FunctionButtonStyle}"
                    Command="{Binding ClearCommand}"/>
            <Button Grid.Row="0" Grid.Column="1" Content="⌫"
                    Style="{StaticResource FunctionButtonStyle}"
                    Command="{Binding BackspaceCommand}"/>
            <Button Grid.Row="0" Grid.Column="2" Content="÷"
                    Style="{StaticResource OperatorButtonStyle}"
                    Command="{Binding OperationCommand}"
                    CommandParameter="÷"/>

            <!-- 第二行：7、8、9、乘法 -->
            <Button Grid.Row="1" Grid.Column="0" Content="7"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="7"/>
            <Button Grid.Row="1" Grid.Column="1" Content="8"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="8"/>
            <Button Grid.Row="1" Grid.Column="2" Content="9"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="9"/>
            <Button Grid.Row="1" Grid.Column="3" Content="×"
                    Style="{StaticResource OperatorButtonStyle}"
                    Command="{Binding OperationCommand}"
                    CommandParameter="×"/>

            <!-- 第三行：4、5、6、减法 -->
            <Button Grid.Row="2" Grid.Column="0" Content="4"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="4"/>
            <Button Grid.Row="2" Grid.Column="1" Content="5"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="5"/>
            <Button Grid.Row="2" Grid.Column="2" Content="6"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="6"/>
            <Button Grid.Row="2" Grid.Column="3" Content="-"
                    Style="{StaticResource OperatorButtonStyle}"
                    Command="{Binding OperationCommand}"
                    CommandParameter="-"/>

            <!-- 第四行：1、2、3、加法 -->
            <Button Grid.Row="3" Grid.Column="0" Content="1"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="1"/>
            <Button Grid.Row="3" Grid.Column="1" Content="2"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="2"/>
            <Button Grid.Row="3" Grid.Column="2" Content="3"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="3"/>
            <Button Grid.Row="3" Grid.Column="3" Content="+"
                    Style="{StaticResource OperatorButtonStyle}"
                    Command="{Binding OperationCommand}"
                    CommandParameter="+"/>

            <!-- 第五行：0、小数点、等号 -->
            <Button Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" Content="0"
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding NumberCommand}"
                    CommandParameter="0"/>
            <Button Grid.Row="4" Grid.Column="2" Content="."
                    Style="{StaticResource NumberButtonStyle}"
                    Command="{Binding DecimalCommand}"/>
            <Button Grid.Row="4" Grid.Column="3" Content="="
                    Style="{StaticResource OperatorButtonStyle}"
                    Command="{Binding EqualsCommand}"/>
        </Grid>
    </Grid>
</Window>
