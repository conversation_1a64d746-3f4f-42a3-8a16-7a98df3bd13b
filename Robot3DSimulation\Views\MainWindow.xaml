<Window x:Class="Robot3DSimulation.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Robot三轴虚拟仿真调试系统" 
        Height="800" Width="1200"
        WindowState="Maximized"
        Icon="pack://application:,,,/Resources/robot_icon.ico">
    
    <Window.Resources>
        <!-- 状态栏样式 -->
        <Style x:Key="StatusBarStyle" TargetType="StatusBar">
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="0,1,0,0"/>
        </Style>
        
        <!-- 工具栏样式 -->
        <Style x:Key="ToolBarStyle" TargetType="ToolBar">
            <Setter Property="Background" Value="#F8F8F8"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 菜单栏 -->
            <RowDefinition Height="Auto"/> <!-- 工具栏 -->
            <RowDefinition Height="*"/>    <!-- 主内容区 -->
            <RowDefinition Height="Auto"/> <!-- 状态栏 -->
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建配置(_N)" Command="{Binding NewConfigCommand}"/>
                <MenuItem Header="打开配置(_O)" Command="{Binding OpenConfigCommand}"/>
                <MenuItem Header="保存配置(_S)" Command="{Binding SaveConfigCommand}"/>
                <Separator/>
                <MenuItem Header="导出数据(_E)" Command="{Binding ExportDataCommand}"/>
                <MenuItem Header="导入数据(_I)" Command="{Binding ImportDataCommand}"/>
                <Separator/>
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}"/>
            </MenuItem>
            
            <MenuItem Header="连接(_C)">
                <MenuItem Header="连接机器人(_C)" Command="{Binding ConnectCommand}" 
                         IsEnabled="{Binding CanConnect}"/>
                <MenuItem Header="断开连接(_D)" Command="{Binding DisconnectCommand}" 
                         IsEnabled="{Binding IsConnected}"/>
                <Separator/>
                <MenuItem Header="连接设置(_S)" Command="{Binding ConnectionSettingsCommand}"/>
                <MenuItem Header="测试连接(_T)" Command="{Binding TestConnectionCommand}"/>
            </MenuItem>
            
            <MenuItem Header="控制(_R)">
                <MenuItem Header="启动(_S)" Command="{Binding StartCommand}" 
                         IsEnabled="{Binding CanStart}"/>
                <MenuItem Header="停止(_T)" Command="{Binding StopCommand}" 
                         IsEnabled="{Binding IsRunning}"/>
                <MenuItem Header="复位(_R)" Command="{Binding ResetCommand}" 
                         IsEnabled="{Binding IsConnected}"/>
                <Separator/>
                <MenuItem Header="紧急停止(_E)" Command="{Binding EmergencyStopCommand}" 
                         IsEnabled="{Binding IsConnected}"/>
                <MenuItem Header="回到原点(_H)" Command="{Binding HomeCommand}" 
                         IsEnabled="{Binding IsConnected}"/>
            </MenuItem>
            
            <MenuItem Header="视图(_V)">
                <MenuItem Header="3D视图(_3)" IsCheckable="True" IsChecked="{Binding Show3DView}"/>
                <MenuItem Header="控制面板(_C)" IsCheckable="True" IsChecked="{Binding ShowControlPanel}"/>
                <MenuItem Header="调试面板(_D)" IsCheckable="True" IsChecked="{Binding ShowDebugPanel}"/>
                <Separator/>
                <MenuItem Header="显示轨迹(_T)" IsCheckable="True" IsChecked="{Binding ShowTrajectory}"/>
                <MenuItem Header="显示坐标轴(_A)" IsCheckable="True" IsChecked="{Binding ShowAxes}"/>
                <MenuItem Header="显示网格(_G)" IsCheckable="True" IsChecked="{Binding ShowGrid}"/>
            </MenuItem>
            
            <MenuItem Header="工具(_T)">
                <MenuItem Header="配置向导(_W)" Command="{Binding ConfigWizardCommand}"/>
                <MenuItem Header="系统诊断(_D)" Command="{Binding DiagnosticsCommand}"/>
                <MenuItem Header="性能监控(_P)" Command="{Binding PerformanceMonitorCommand}"/>
                <Separator/>
                <MenuItem Header="截图(_S)" Command="{Binding ScreenshotCommand}"/>
                <MenuItem Header="录制视频(_V)" Command="{Binding RecordVideoCommand}"/>
            </MenuItem>
            
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="用户手册(_M)" Command="{Binding UserManualCommand}"/>
                <MenuItem Header="快捷键(_K)" Command="{Binding ShortcutsCommand}"/>
                <Separator/>
                <MenuItem Header="检查更新(_U)" Command="{Binding CheckUpdateCommand}"/>
                <MenuItem Header="关于(_A)" Command="{Binding AboutCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="1" Style="{StaticResource ToolBarStyle}">
            <Button Command="{Binding ConnectCommand}" ToolTip="连接机器人" 
                    IsEnabled="{Binding CanConnect}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔗" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="连接"/>
                </StackPanel>
            </Button>
            
            <Button Command="{Binding DisconnectCommand}" ToolTip="断开连接" 
                    IsEnabled="{Binding IsConnected}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔌" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="断开"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <Button Command="{Binding StartCommand}" ToolTip="启动" 
                    IsEnabled="{Binding CanStart}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="▶️" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="启动"/>
                </StackPanel>
            </Button>
            
            <Button Command="{Binding StopCommand}" ToolTip="停止" 
                    IsEnabled="{Binding IsRunning}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="⏹️" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="停止"/>
                </StackPanel>
            </Button>
            
            <Button Command="{Binding EmergencyStopCommand}" ToolTip="紧急停止" 
                    IsEnabled="{Binding IsConnected}" Background="#FF4444" Foreground="White">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🛑" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="急停"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <Button Command="{Binding ResetCommand}" ToolTip="复位" 
                    IsEnabled="{Binding IsConnected}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="复位"/>
                </StackPanel>
            </Button>
            
            <Button Command="{Binding HomeCommand}" ToolTip="回到原点" 
                    IsEnabled="{Binding IsConnected}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🏠" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="原点"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <ToggleButton IsChecked="{Binding ShowTrajectory}" ToolTip="显示轨迹">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📈" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="轨迹"/>
                </StackPanel>
            </ToggleButton>
            
            <Button Command="{Binding ScreenshotCommand}" ToolTip="截图">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📷" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="截图"/>
                </StackPanel>
            </Button>
        </ToolBar>

        <!-- 主内容区 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>      <!-- 3D视图 -->
                <ColumnDefinition Width="Auto"/>   <!-- 分隔符 -->
                <ColumnDefinition Width="300"/>    <!-- 右侧面板 -->
            </Grid.ColumnDefinitions>

            <!-- 3D视图区域 -->
            <Border Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="1" Margin="5">
                <ContentPresenter Content="{Binding Robot3DView}"/>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" 
                         VerticalAlignment="Stretch" Background="#DDDDDD"/>

            <!-- 右侧面板 -->
            <Grid Grid.Column="2" Margin="5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>    <!-- 控制面板 -->
                    <RowDefinition Height="Auto"/> <!-- 分隔符 -->
                    <RowDefinition Height="*"/>    <!-- 调试面板 -->
                </Grid.RowDefinitions>

                <!-- 控制面板 -->
                <Border Grid.Row="0" BorderBrush="#CCCCCC" BorderThickness="1">
                    <ContentPresenter Content="{Binding ControlPanel}"/>
                </Border>

                <!-- 分隔符 -->
                <GridSplitter Grid.Row="1" Height="5" HorizontalAlignment="Stretch" 
                             VerticalAlignment="Center" Background="#DDDDDD"/>

                <!-- 调试面板 -->
                <Border Grid.Row="2" BorderBrush="#CCCCCC" BorderThickness="1">
                    <ContentPresenter Content="{Binding DebugPanel}"/>
                </Border>
            </Grid>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding ConnectionStatus}" FontWeight="Bold"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding RobotStatus}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="T: "/>
                    <TextBlock Text="{Binding CurrentTPosition, StringFormat=F2}"/>
                    <TextBlock Text=" mm"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="R: "/>
                    <TextBlock Text="{Binding CurrentRPosition, StringFormat=F2}"/>
                    <TextBlock Text=" °"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="Z: "/>
                    <TextBlock Text="{Binding CurrentZPosition, StringFormat=F2}"/>
                    <TextBlock Text=" mm"/>
                </StackPanel>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding CurrentTime, StringFormat=yyyy-MM-dd HH:mm:ss}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
