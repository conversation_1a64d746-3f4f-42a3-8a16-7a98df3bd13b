<Application x:Class="Robot3DSimulation.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <!-- 全局样式资源 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 可以在这里添加主题样式 -->
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局颜色定义 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            
            <!-- 按钮样式 -->
            <Style x:Key="PrimaryButton" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="Padding" Value="10,5"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Opacity" Value="0.8"/>
                    </Trigger>
                    <Trigger Property="IsEnabled" Value="False">
                        <Setter Property="Opacity" Value="0.5"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
            
            <!-- 文本框样式 -->
            <Style x:Key="ModernTextBox" TargetType="TextBox">
                <Setter Property="Padding" Value="8,5"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="BorderBrush" Value="#CCCCCC"/>
            </Style>
            
            <!-- 标签样式 -->
            <Style x:Key="HeaderLabel" TargetType="Label">
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Margin" Value="5"/>
            </Style>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
