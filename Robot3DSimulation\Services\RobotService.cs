using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Robot3DSimulation.Models;

namespace Robot3DSimulation.Services
{
    /// <summary>
    /// 机器人服务实现
    /// </summary>
    public class RobotService : IRobotService
    {
        private readonly ILogger<RobotService> _logger;
        private readonly IModbusService _modbusService;
        private readonly IConfigurationService _configService;
        private readonly RobotModel _robotModel;
        private Timer? _updateTimer;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _disposed;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; private set; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning { get; private set; }

        /// <summary>
        /// 机器人模型
        /// </summary>
        public RobotModel Robot => _robotModel;

        /// <summary>
        /// 机器人模型（兼容性属性）
        /// </summary>
        public RobotModel Model => _robotModel;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event EventHandler<bool>? ConnectionStatusChanged;

        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event EventHandler<RobotStatusChangedEventArgs>? StatusChanged;

        /// <summary>
        /// 位置变化事件
        /// </summary>
        public event EventHandler<RobotPositionChangedEventArgs>? PositionChanged;

        /// <summary>
        /// ARM状态变化事件
        /// </summary>
        public event EventHandler<ArmStatusChangedEventArgs>? ArmStatusChanged;

        /// <summary>
        /// 轨迹记录事件
        /// </summary>
        public event EventHandler<TrajectoryRecordedEventArgs>? TrajectoryRecorded;

        /// <summary>
        /// 安全状态变化事件
        /// </summary>
        public event EventHandler<SafetyStatusChangedEventArgs>? SafetyStatusChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="modbusService">Modbus服务</param>
        /// <param name="configService">配置服务</param>
        public RobotService(
            ILogger<RobotService> logger,
            IModbusService modbusService,
            IConfigurationService configService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _modbusService = modbusService ?? throw new ArgumentNullException(nameof(modbusService));
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));

            // 初始化机器人模型
            _robotModel = new RobotModel();
            InitializeRobotModel();

            // 订阅Modbus服务事件
            _modbusService.ConnectionStatusChanged += OnModbusConnectionChanged;
            _modbusService.CommunicationError += OnModbusCommunicationError;

            _logger.LogInformation("机器人服务已初始化");
        }

        /// <summary>
        /// 连接到机器人
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectAsync()
        {
            try
            {
                _logger.LogInformation("正在连接到机器人...");

                // 连接Modbus
                var connected = await _modbusService.ConnectAsync(_configService.ModbusConfig);
                
                if (connected)
                {
                    IsConnected = true;
                    ConnectionStatusChanged?.Invoke(this, true);
                    StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs("已连接", 1, false, false));
                    
                    _logger.LogInformation("机器人连接成功");
                    
                    // 读取初始位置
                    await ReadCurrentPositionsAsync();
                }
                else
                {
                    _logger.LogError("机器人连接失败");
                    StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs("连接失败", -1, true, false));
                }

                return connected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接机器人时发生错误");
                StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs($"连接错误: {ex.Message}", -1, true, false));
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns>断开任务</returns>
        public async Task DisconnectAsync()
        {
            try
            {
                // 停止数据更新
                await StopDataUpdateAsync();

                // 断开Modbus连接
                await _modbusService.DisconnectAsync();

                IsConnected = false;
                ConnectionStatusChanged?.Invoke(this, false);
                StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs("已断开", 0, false, false));

                _logger.LogInformation("机器人连接已断开");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开机器人连接时发生错误");
            }
        }

        /// <summary>
        /// 启动数据更新
        /// </summary>
        /// <returns>启动任务</returns>
        public async Task StartDataUpdateAsync()
        {
            if (!IsConnected)
                throw new InvalidOperationException("机器人未连接");

            if (IsRunning)
                return;

            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                var updateInterval = TimeSpan.FromMilliseconds(1000.0 / _configService.RobotConfig.UpdateFrequency);

                _updateTimer = new Timer(async _ => await UpdateDataAsync(), null, TimeSpan.Zero, updateInterval);

                IsRunning = true;
                StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs("数据更新已启动", 2, false, true));

                _logger.LogInformation("机器人数据更新已启动，频率: {Frequency} Hz", _configService.RobotConfig.UpdateFrequency);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动数据更新时发生错误");
                StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs($"启动错误: {ex.Message}", -1, true, false));
                throw;
            }
        }

        /// <summary>
        /// 停止数据更新
        /// </summary>
        /// <returns>停止任务</returns>
        public async Task StopDataUpdateAsync()
        {
            try
            {
                _updateTimer?.Dispose();
                _updateTimer = null;

                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                IsRunning = false;
                StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs("数据更新已停止", 0, false, false));

                _logger.LogInformation("机器人数据更新已停止");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止数据更新时发生错误");
            }
        }

        /// <summary>
        /// 移动T轴到指定位置
        /// </summary>
        /// <param name="position">目标位置</param>
        /// <returns>移动是否成功</returns>
        public async Task<bool> MoveTAxisAsync(double position)
        {
            if (!IsConnected)
                return false;

            try
            {
                var registerMap = _configService.ModbusConfig.RegisterMap;
                var targetRegister = registerMap.TAxisTarget;

                var scaledValue = (float)(position * targetRegister.Scale);
                var success = await _modbusService.WriteFloat32Async(targetRegister.Address, scaledValue);

                if (success)
                {
                    _robotModel.TAxis.SetTarget(position);
                    _logger.LogInformation("T轴目标位置已设置: {Position}", position);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动T轴时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 移动R轴到指定角度
        /// </summary>
        /// <param name="angle">目标角度</param>
        /// <returns>移动是否成功</returns>
        public async Task<bool> MoveRAxisAsync(double angle)
        {
            if (!IsConnected)
                return false;

            try
            {
                var registerMap = _configService.ModbusConfig.RegisterMap;
                var targetRegister = registerMap.RAxisTarget;

                var scaledValue = (float)(angle * targetRegister.Scale);
                var success = await _modbusService.WriteFloat32Async(targetRegister.Address, scaledValue);

                if (success)
                {
                    _robotModel.RAxis.SetTarget(angle);
                    _logger.LogInformation("R轴目标角度已设置: {Angle}", angle);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动R轴时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 移动Z轴到指定高度
        /// </summary>
        /// <param name="height">目标高度</param>
        /// <returns>移动是否成功</returns>
        public async Task<bool> MoveZAxisAsync(double height)
        {
            if (!IsConnected)
                return false;

            try
            {
                var registerMap = _configService.ModbusConfig.RegisterMap;
                var targetRegister = registerMap.ZAxisTarget;

                var scaledValue = (float)(height * targetRegister.Scale);
                var success = await _modbusService.WriteFloat32Async(targetRegister.Address, scaledValue);

                if (success)
                {
                    _robotModel.ZAxis.SetTarget(height);
                    _logger.LogInformation("Z轴目标高度已设置: {Height}", height);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动Z轴时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="tPosition">T轴位置</param>
        /// <param name="rAngle">R轴角度</param>
        /// <param name="zHeight">Z轴高度</param>
        /// <returns>移动是否成功</returns>
        public async Task<bool> MoveToPositionAsync(double tPosition, double rAngle, double zHeight)
        {
            try
            {
                var tasks = new[]
                {
                    MoveTAxisAsync(tPosition),
                    MoveRAxisAsync(rAngle),
                    MoveZAxisAsync(zHeight)
                };

                var results = await Task.WhenAll(tasks);
                var success = Array.TrueForAll(results, r => r);

                if (success)
                {
                    _logger.LogInformation("机器人移动到位置: T={TPosition}, R={RAngle}, Z={ZHeight}",
                        tPosition, rAngle, zHeight);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动到指定位置时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 停止所有轴运动
        /// </summary>
        /// <returns>停止是否成功</returns>
        public async Task<bool> StopAllAxesAsync()
        {
            if (!IsConnected)
                return false;

            try
            {
                // 将目标位置设置为当前位置
                var tasks = new[]
                {
                    MoveTAxisAsync(_robotModel.TAxis.CurrentPosition),
                    MoveRAxisAsync(_robotModel.RAxis.CurrentPosition),
                    MoveZAxisAsync(_robotModel.ZAxis.CurrentPosition)
                };

                var results = await Task.WhenAll(tasks);
                var success = Array.TrueForAll(results, r => r);

                if (success)
                {
                    _logger.LogInformation("所有轴运动已停止");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止轴运动时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 设置紧急停止状态
        /// </summary>
        /// <param name="emergencyStop">紧急停止状态</param>
        /// <returns>设置是否成功</returns>
        public async Task<bool> SetEmergencyStopAsync(bool emergencyStop)
        {
            if (!IsConnected)
                return false;

            try
            {
                var registerMap = _configService.ModbusConfig.RegisterMap;
                var emergencyRegister = registerMap.EmergencyStop;

                var success = await _modbusService.WriteHoldingRegisterAsync(emergencyRegister.Address,
                    (ushort)(emergencyStop ? 1 : 0));

                if (success)
                {
                    _logger.LogWarning("紧急停止状态已设置: {EmergencyStop}", emergencyStop);
                    StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs(
                        emergencyStop ? "紧急停止激活" : "紧急停止解除",
                        emergencyStop ? -2 : 0,
                        emergencyStop,
                        false));
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置紧急停止时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 复位机器人
        /// </summary>
        /// <returns>复位是否成功</returns>
        public async Task<bool> ResetAsync()
        {
            if (!IsConnected)
                return false;

            try
            {
                // 先解除紧急停止
                await SetEmergencyStopAsync(false);

                // 停止所有运动
                await StopAllAxesAsync();

                _logger.LogInformation("机器人已复位");
                StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs("已复位", 1, false, false));

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复位机器人时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 回到原点
        /// </summary>
        /// <returns>归零是否成功</returns>
        public async Task<bool> HomeAsync()
        {
            if (!IsConnected)
                return false;

            try
            {
                var config = _configService.RobotConfig;
                var homePosition = new
                {
                    T = (config.TAxis.MinLimit + config.TAxis.MaxLimit) / 2,
                    R = 0.0,
                    Z = config.ZAxis.MinLimit + 50 // 稍微抬高一点
                };

                var success = await MoveToPositionAsync(homePosition.T, homePosition.R, homePosition.Z);

                if (success)
                {
                    _logger.LogInformation("机器人正在回到原点");
                    StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs("正在归零", 3, false, true));
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "回到原点时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 设置ARM状态
        /// </summary>
        /// <param name="armEndType">ARM端类型</param>
        /// <param name="status">ARM状态</param>
        /// <returns>设置是否成功</returns>
        public async Task<bool> SetArmStatusAsync(ArmEndType armEndType, ArmStatus status)
        {
            if (!IsConnected)
                return false;

            try
            {
                var registerMap = _configService.ModbusConfig.RegisterMap;
                var register = armEndType == ArmEndType.Norse ?
                    registerMap.NorseArmStatus : registerMap.SmoothArmStatus;

                var success = await _modbusService.WriteHoldingRegisterAsync(register.Address, (ushort)status);

                if (success)
                {
                    if (armEndType == ArmEndType.Norse)
                    {
                        _robotModel.NorseArm.Status = status;
                        _robotModel.NorseArm.LastUpdate = DateTime.Now;
                    }
                    else
                    {
                        _robotModel.SmoothArm.Status = status;
                        _robotModel.SmoothArm.LastUpdate = DateTime.Now;
                    }

                    _logger.LogInformation("{ArmType} ARM状态已设置: {Status}", armEndType, status);
                    ArmStatusChanged?.Invoke(this, new ArmStatusChangedEventArgs(armEndType, status));
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置ARM状态时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取当前位置
        /// </summary>
        /// <returns>当前位置</returns>
        public (double T, double R, double Z) GetCurrentPosition()
        {
            return (_robotModel.TAxis.CurrentPosition,
                    _robotModel.RAxis.CurrentPosition,
                    _robotModel.ZAxis.CurrentPosition);
        }

        /// <summary>
        /// 获取ARM状态
        /// </summary>
        /// <param name="armEndType">ARM端类型</param>
        /// <returns>ARM状态</returns>
        public ArmStatus GetArmStatus(ArmEndType armEndType)
        {
            return armEndType == ArmEndType.Norse ?
                _robotModel.NorseArm.Status : _robotModel.SmoothArm.Status;
        }

        /// <summary>
        /// 获取轨迹点
        /// </summary>
        /// <returns>轨迹点数组</returns>
        public TrajectoryPoint[] GetTrajectoryPoints()
        {
            return _robotModel.GetTrajectoryPoints().ToArray();
        }

        /// <summary>
        /// 清除轨迹记录
        /// </summary>
        public void ClearTrajectory()
        {
            _robotModel.ClearTrajectory();
            _logger.LogInformation("轨迹记录已清除");
        }

        /// <summary>
        /// 检查安全状态
        /// </summary>
        /// <returns>是否安全</returns>
        public bool CheckSafetyStatus()
        {
            return _robotModel.IsInSafeRange(
                _robotModel.TAxis.CurrentPosition,
                _robotModel.RAxis.CurrentPosition,
                _robotModel.ZAxis.CurrentPosition);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                // 停止数据更新
                StopDataUpdateAsync().Wait(5000);

                // 断开连接
                DisconnectAsync().Wait(5000);

                // 取消订阅事件
                if (_modbusService != null)
                {
                    _modbusService.ConnectionStatusChanged -= OnModbusConnectionChanged;
                    _modbusService.CommunicationError -= OnModbusCommunicationError;
                }

                // 释放定时器
                _updateTimer?.Dispose();
                _cancellationTokenSource?.Dispose();

                _disposed = true;
                _logger.LogInformation("机器人服务已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放机器人服务时发生错误");
            }
        }

        /// <summary>
        /// 初始化机器人模型
        /// </summary>
        private void InitializeRobotModel()
        {
            var config = _configService.RobotConfig;

            // 设置轴限制
            _robotModel.TAxis.SetLimits(config.TAxis.MinLimit, config.TAxis.MaxLimit);
            _robotModel.TAxis.SafetyMargin = config.TAxis.SafetyMargin;

            _robotModel.RAxis.SetLimits(config.RAxis.MinLimit, config.RAxis.MaxLimit);
            _robotModel.RAxis.SafetyMargin = config.RAxis.SafetyMargin;

            _robotModel.ZAxis.SetLimits(config.ZAxis.MinLimit, config.ZAxis.MaxLimit);
            _robotModel.ZAxis.SafetyMargin = config.ZAxis.SafetyMargin;

            // 设置轨迹缓冲区大小
            _robotModel.SetTrajectoryBufferSize(config.TrajectoryBufferSize);

            _logger.LogInformation("机器人模型已初始化");
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        private async Task UpdateDataAsync()
        {
            if (!IsConnected || _cancellationTokenSource?.Token.IsCancellationRequested == true)
                return;

            try
            {
                // 读取当前位置
                await ReadCurrentPositionsAsync();

                // 读取ARM状态
                await ReadArmStatusAsync();

                // 检查安全状态
                var isSafe = _robotModel.IsInSafeRange(
                    _robotModel.TAxis.CurrentPosition,
                    _robotModel.RAxis.CurrentPosition,
                    _robotModel.ZAxis.CurrentPosition);
                SafetyStatusChanged?.Invoke(this, new SafetyStatusChangedEventArgs(
                    isSafe ? "所有轴在安全范围内" : "存在轴超出安全范围",
                    isSafe,
                    _robotModel.TAxis.CurrentPosition,
                    _robotModel.RAxis.CurrentPosition,
                    _robotModel.ZAxis.CurrentPosition,
                    _robotModel.TAxis.CurrentPosition)); // 使用T轴位置作为currentPosition参数

                // 记录轨迹点
                var trajectoryPoint = new TrajectoryPoint
                {
                    T = _robotModel.TAxis.CurrentPosition,
                    R = _robotModel.RAxis.CurrentPosition,
                    Z = _robotModel.ZAxis.CurrentPosition,
                    Timestamp = DateTime.Now
                };

                _robotModel.AddTrajectoryPoint(trajectoryPoint.T, trajectoryPoint.R, trajectoryPoint.Z);
                TrajectoryRecorded?.Invoke(this, new TrajectoryRecordedEventArgs(trajectoryPoint, _robotModel.TrajectoryPoints.Count));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新数据时发生错误");
            }
        }

        /// <summary>
        /// 读取当前位置
        /// </summary>
        private async Task ReadCurrentPositionsAsync()
        {
            try
            {
                var registerMap = _configService.ModbusConfig.RegisterMap;

                // 读取T轴位置
                var tAxisValue = await _modbusService.ReadFloat32Async(registerMap.TAxisPosition.Address);
                var tPosition = tAxisValue / registerMap.TAxisPosition.Scale;
                var oldTPosition = _robotModel.TAxis.CurrentPosition;
                _robotModel.TAxis.UpdatePosition(tPosition);

                if (Math.Abs(tPosition - oldTPosition) > 0.01)
                {
                    PositionChanged?.Invoke(this, new RobotPositionChangedEventArgs(
                        "T", tPosition, oldTPosition, _robotModel.TAxis.IsInSafeRange));
                }

                // 读取R轴位置
                var rAxisValue = await _modbusService.ReadFloat32Async(registerMap.RAxisPosition.Address);
                var rPosition = rAxisValue / registerMap.RAxisPosition.Scale;
                var oldRPosition = _robotModel.RAxis.CurrentPosition;
                _robotModel.RAxis.UpdatePosition(rPosition);

                if (Math.Abs(rPosition - oldRPosition) > 0.01)
                {
                    PositionChanged?.Invoke(this, new RobotPositionChangedEventArgs(
                        "R", rPosition, oldRPosition, _robotModel.RAxis.IsInSafeRange));
                }

                // 读取Z轴位置
                var zAxisValue = await _modbusService.ReadFloat32Async(registerMap.ZAxisPosition.Address);
                var zPosition = zAxisValue / registerMap.ZAxisPosition.Scale;
                var oldZPosition = _robotModel.ZAxis.CurrentPosition;
                _robotModel.ZAxis.UpdatePosition(zPosition);

                if (Math.Abs(zPosition - oldZPosition) > 0.01)
                {
                    PositionChanged?.Invoke(this, new RobotPositionChangedEventArgs(
                        "Z", zPosition, oldZPosition, _robotModel.ZAxis.IsInSafeRange));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取位置数据时发生错误");
            }
        }

        /// <summary>
        /// 读取ARM状态
        /// </summary>
        private async Task ReadArmStatusAsync()
        {
            try
            {
                var registerMap = _configService.ModbusConfig.RegisterMap;

                // 读取Norse ARM状态
                var norseValue = await _modbusService.ReadHoldingRegisterAsync(registerMap.NorseArmStatus.Address);
                var norseStatus = (ArmStatus)norseValue;
                var oldNorseStatus = _robotModel.NorseArm.Status;
                _robotModel.NorseArm.Status = norseStatus;
                _robotModel.NorseArm.LastUpdate = DateTime.Now;

                if (norseStatus != oldNorseStatus)
                {
                    ArmStatusChanged?.Invoke(this, new ArmStatusChangedEventArgs(
                        ArmEndType.Norse, norseStatus, $"状态从 {oldNorseStatus} 变更为 {norseStatus}"));
                }

                // 读取Smooth ARM状态
                var smoothValue = await _modbusService.ReadHoldingRegisterAsync(registerMap.SmoothArmStatus.Address);
                var smoothStatus = (ArmStatus)smoothValue;
                var oldSmoothStatus = _robotModel.SmoothArm.Status;
                _robotModel.SmoothArm.Status = smoothStatus;
                _robotModel.SmoothArm.LastUpdate = DateTime.Now;

                if (smoothStatus != oldSmoothStatus)
                {
                    ArmStatusChanged?.Invoke(this, new ArmStatusChangedEventArgs(
                        ArmEndType.Smooth, smoothStatus, $"状态从 {oldSmoothStatus} 变更为 {smoothStatus}"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取ARM状态时发生错误");
            }
        }

        /// <summary>
        /// Modbus连接状态变化处理
        /// </summary>
        private void OnModbusConnectionChanged(object? sender, bool isConnected)
        {
            IsConnected = isConnected;
            ConnectionStatusChanged?.Invoke(this, isConnected);

            if (!isConnected && IsRunning)
            {
                _ = Task.Run(async () => await StopDataUpdateAsync());
            }
        }

        /// <summary>
        /// Modbus通信错误处理
        /// </summary>
        private void OnModbusCommunicationError(object? sender, string errorMessage)
        {
            _logger.LogError("Modbus通信错误: {ErrorMessage}", errorMessage);
            StatusChanged?.Invoke(this, new RobotStatusChangedEventArgs($"通信错误: {errorMessage}", -1, true, false));
        }

        /// <summary>
        /// 设置运行模式
        /// </summary>
        /// <param name="autoMode">是否自动模式</param>
        /// <returns>设置任务</returns>
        public async Task<bool> SetRunModeAsync(bool autoMode)
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法设置运行模式");
                    return false;
                }

                var registerMap = _configService.ModbusConfig.RegisterMap;
                var success = await _modbusService.WriteHoldingRegisterAsync(
                    registerMap.RunMode,
                    (ushort)(autoMode ? 1 : 0));

                if (success)
                {
                    _logger.LogInformation("运行模式已设置为: {Mode}", autoMode ? "自动" : "手动");
                }
                else
                {
                    _logger.LogError("设置运行模式失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置运行模式时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 更新ARM端状态
        /// </summary>
        /// <param name="endType">ARM端类型</param>
        /// <param name="status">状态</param>
        /// <param name="hasWafer">是否有晶圆</param>
        /// <param name="waferId">晶圆ID</param>
        /// <returns>更新任务</returns>
        public async Task UpdateArmEndStatusAsync(ArmEndType endType, ArmStatus status, bool hasWafer = false, string? waferId = null)
        {
            try
            {
                if (endType == ArmEndType.Norse)
                {
                    _robotModel.NorseEnd.Status = status;
                    _robotModel.NorseEnd.HasWafer = hasWafer;
                    _robotModel.NorseEnd.WaferId = waferId;
                }
                else
                {
                    _robotModel.SmoothEnd.Status = status;
                    _robotModel.SmoothEnd.HasWafer = hasWafer;
                    _robotModel.SmoothEnd.WaferId = waferId;
                }

                // 触发ARM状态变化事件
                var description = hasWafer ? $"状态: {status}, 晶圆ID: {waferId}" : $"状态: {status}, 无晶圆";
                ArmStatusChanged?.Invoke(this, new ArmStatusChangedEventArgs(endType, status, description));

                _logger.LogInformation("{EndType} ARM状态已更新: {Status}", endType, status);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新ARM端状态时发生错误");
            }
        }

        /// <summary>
        /// 移动单个轴到指定位置
        /// </summary>
        /// <param name="axisName">轴名称（T、R、Z）</param>
        /// <param name="position">目标位置</param>
        /// <returns>移动是否成功</returns>
        public async Task<bool> MoveAxisAsync(string axisName, double position)
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法移动轴");
                    return false;
                }

                var registerMap = _configService.ModbusConfig.RegisterMap;
                bool success = false;

                switch (axisName.ToUpper())
                {
                    case "T":
                        if (_robotModel.TAxis.IsPositionValid(position))
                        {
                            var scaledValue = (float)(position * 1.0); // 默认缩放因子
                            success = await _modbusService.WriteFloat32Async(registerMap.TAxisTargetPosition, scaledValue);
                            if (success)
                            {
                                _robotModel.TAxis.SetTargetPosition(position);
                            }
                        }
                        break;
                    case "R":
                        if (_robotModel.RAxis.IsPositionValid(position))
                        {
                            var scaledValue = (float)(position * 1.0); // 默认缩放因子
                            success = await _modbusService.WriteFloat32Async(registerMap.RAxisTargetPosition, scaledValue);
                            if (success)
                            {
                                _robotModel.RAxis.SetTargetPosition(position);
                            }
                        }
                        break;
                    case "Z":
                        if (_robotModel.ZAxis.IsPositionValid(position))
                        {
                            var scaledValue = (float)(position * 1.0); // 默认缩放因子
                            success = await _modbusService.WriteFloat32Async(registerMap.ZAxisTargetPosition, scaledValue);
                            if (success)
                            {
                                _robotModel.ZAxis.SetTargetPosition(position);
                            }
                        }
                        break;
                    default:
                        _logger.LogError("未知的轴名称: {AxisName}", axisName);
                        return false;
                }

                if (success)
                {
                    _logger.LogInformation("{AxisName}轴移动到位置: {Position}", axisName, position);
                }
                else
                {
                    _logger.LogError("{AxisName}轴移动失败", axisName);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动{AxisName}轴时发生错误", axisName);
                return false;
            }
        }

        /// <summary>
        /// 获取当前位置
        /// </summary>
        /// <returns>当前三轴位置</returns>
        public async Task<AxisPosition[]> GetCurrentPositionsAsync()
        {
            try
            {
                await ReadCurrentPositionsAsync();
                return new AxisPosition[]
                {
                    _robotModel.TAxis,
                    _robotModel.RAxis,
                    _robotModel.ZAxis
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前位置时发生错误");
                return new AxisPosition[]
                {
                    _robotModel.TAxis,
                    _robotModel.RAxis,
                    _robotModel.ZAxis
                };
            }
        }

        /// <summary>
        /// 检查位置是否安全
        /// </summary>
        /// <param name="t">T轴位置</param>
        /// <param name="r">R轴位置</param>
        /// <param name="z">Z轴位置</param>
        /// <returns>是否安全</returns>
        public bool IsPositionSafe(double t, double r, double z)
        {
            return _robotModel.TAxis.IsPositionValid(t) &&
                   _robotModel.RAxis.IsPositionValid(r) &&
                   _robotModel.ZAxis.IsPositionValid(z);
        }

        /// <summary>
        /// 获取机器人状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        public async Task<string> GetStatusInfoAsync()
        {
            try
            {
                var status = $"连接状态: {(IsConnected ? "已连接" : "未连接")}\n";
                status += $"运行状态: {(IsRunning ? "运行中" : "已停止")}\n";
                status += $"T轴位置: {_robotModel.TAxis.CurrentPosition:F3} ({(_robotModel.TAxis.IsInSafeRange ? "安全" : "超限")})\n";
                status += $"R轴位置: {_robotModel.RAxis.CurrentPosition:F3} ({(_robotModel.RAxis.IsInSafeRange ? "安全" : "超限")})\n";
                status += $"Z轴位置: {_robotModel.ZAxis.CurrentPosition:F3} ({(_robotModel.ZAxis.IsInSafeRange ? "安全" : "超限")})\n";
                status += $"Norse ARM: {_robotModel.NorseEnd.Status} ({(_robotModel.NorseEnd.HasWafer ? "有晶圆" : "无晶圆")})\n";
                status += $"Smooth ARM: {_robotModel.SmoothEnd.Status} ({(_robotModel.SmoothEnd.HasWafer ? "有晶圆" : "无晶圆")})";

                await Task.CompletedTask;
                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取状态信息时发生错误");
                return "状态信息获取失败";
            }
        }
    }
}
