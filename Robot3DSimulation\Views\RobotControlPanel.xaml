<UserControl x:Class="Robot3DSimulation.Views.RobotControlPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="300">
    
    <UserControl.Resources>
        <!-- 控制面板样式 -->
        <Style x:Key="AxisGroupStyle" TargetType="GroupBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="PositionTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>
        
        <Style x:Key="SliderStyle" TargetType="Slider">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Height" Value="25"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel>
            <!-- 标题 -->
            <TextBlock Text="机器人控制面板" FontSize="16" FontWeight="Bold" 
                       HorizontalAlignment="Center" Margin="5,10"/>
            
            <!-- 连接状态 -->
            <Border Background="#F0F0F0" Margin="5" Padding="5" CornerRadius="3">
                <StackPanel>
                    <TextBlock Text="连接状态" FontWeight="Bold"/>
                    <TextBlock Text="{Binding ConnectionStatus}" 
                               Foreground="{Binding ConnectionStatusColor}"/>
                </StackPanel>
            </Border>
            
            <!-- T轴控制 -->
            <GroupBox Header="T轴 (平移轴)" Style="{StaticResource AxisGroupStyle}">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="当前位置:"/>
                        <TextBlock Grid.Column="1" Text="{Binding TAxisPosition, StringFormat=F2}" 
                                   Style="{StaticResource PositionTextStyle}"/>
                    </Grid>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="目标位置:"/>
                        <TextBox Grid.Column="1" Text="{Binding TAxisTarget, UpdateSourceTrigger=PropertyChanged}" 
                                 Width="80" HorizontalAlignment="Right"/>
                    </Grid>
                    
                    <Slider Style="{StaticResource SliderStyle}"
                            Minimum="{Binding TAxisMinLimit}"
                            Maximum="{Binding TAxisMaxLimit}"
                            Value="{Binding TAxisTarget}"
                            TickFrequency="10"
                            TickPlacement="BottomRight"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Content="移动到目标" Margin="2"
                                Command="{Binding MoveTAxisCommand}"/>
                        <Button Grid.Column="1" Content="停止" Margin="2"
                                Command="{Binding StopTAxisCommand}"/>
                    </Grid>
                    
                    <ProgressBar Value="{Binding TAxisProgress}" Height="5" Margin="0,5"/>
                    
                    <TextBlock Text="{Binding TAxisStatus}" FontSize="10" 
                               Foreground="{Binding TAxisStatusColor}"/>
                </StackPanel>
            </GroupBox>
            
            <!-- R轴控制 -->
            <GroupBox Header="R轴 (旋转轴)" Style="{StaticResource AxisGroupStyle}">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="当前角度:"/>
                        <TextBlock Grid.Column="1" Text="{Binding RAxisPosition, StringFormat=F2}" 
                                   Style="{StaticResource PositionTextStyle}"/>
                    </Grid>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="目标角度:"/>
                        <TextBox Grid.Column="1" Text="{Binding RAxisTarget, UpdateSourceTrigger=PropertyChanged}" 
                                 Width="80" HorizontalAlignment="Right"/>
                    </Grid>
                    
                    <Slider Style="{StaticResource SliderStyle}"
                            Minimum="{Binding RAxisMinLimit}"
                            Maximum="{Binding RAxisMaxLimit}"
                            Value="{Binding RAxisTarget}"
                            TickFrequency="15"
                            TickPlacement="BottomRight"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Content="旋转到目标" Margin="2"
                                Command="{Binding MoveRAxisCommand}"/>
                        <Button Grid.Column="1" Content="停止" Margin="2"
                                Command="{Binding StopRAxisCommand}"/>
                    </Grid>
                    
                    <ProgressBar Value="{Binding RAxisProgress}" Height="5" Margin="0,5"/>
                    
                    <TextBlock Text="{Binding RAxisStatus}" FontSize="10" 
                               Foreground="{Binding RAxisStatusColor}"/>
                </StackPanel>
            </GroupBox>
            
            <!-- Z轴控制 -->
            <GroupBox Header="Z轴 (垂直轴)" Style="{StaticResource AxisGroupStyle}">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="当前高度:"/>
                        <TextBlock Grid.Column="1" Text="{Binding ZAxisPosition, StringFormat=F2}" 
                                   Style="{StaticResource PositionTextStyle}"/>
                    </Grid>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="目标高度:"/>
                        <TextBox Grid.Column="1" Text="{Binding ZAxisTarget, UpdateSourceTrigger=PropertyChanged}" 
                                 Width="80" HorizontalAlignment="Right"/>
                    </Grid>
                    
                    <Slider Style="{StaticResource SliderStyle}"
                            Minimum="{Binding ZAxisMinLimit}"
                            Maximum="{Binding ZAxisMaxLimit}"
                            Value="{Binding ZAxisTarget}"
                            TickFrequency="5"
                            TickPlacement="BottomRight"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Content="移动到目标" Margin="2"
                                Command="{Binding MoveZAxisCommand}"/>
                        <Button Grid.Column="1" Content="停止" Margin="2"
                                Command="{Binding StopZAxisCommand}"/>
                    </Grid>
                    
                    <ProgressBar Value="{Binding ZAxisProgress}" Height="5" Margin="0,5"/>
                    
                    <TextBlock Text="{Binding ZAxisStatus}" FontSize="10" 
                               Foreground="{Binding ZAxisStatusColor}"/>
                </StackPanel>
            </GroupBox>
            
            <!-- ARM控制 -->
            <GroupBox Header="ARM控制" Style="{StaticResource AxisGroupStyle}">
                <StackPanel>
                    <TextBlock Text="Norse端状态:" FontWeight="Bold"/>
                    <TextBlock Text="{Binding NorseArmStatus}" Margin="10,0,0,5"/>
                    
                    <TextBlock Text="Smooth端状态:" FontWeight="Bold"/>
                    <TextBlock Text="{Binding SmoothArmStatus}" Margin="10,0,0,5"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Content="抓取" Margin="2"
                                Command="{Binding GripCommand}"/>
                        <Button Grid.Column="1" Content="释放" Margin="2"
                                Command="{Binding ReleaseCommand}"/>
                    </Grid>
                </StackPanel>
            </GroupBox>
            
            <!-- 快速操作 -->
            <GroupBox Header="快速操作" Style="{StaticResource AxisGroupStyle}">
                <StackPanel>
                    <Button Content="回到原点" Margin="2" 
                            Command="{Binding HomeCommand}"/>
                    <Button Content="紧急停止" Margin="2" Background="#FF4444" Foreground="White"
                            Command="{Binding EmergencyStopCommand}"/>
                    <Button Content="复位" Margin="2"
                            Command="{Binding ResetCommand}"/>
                </StackPanel>
            </GroupBox>
            
            <!-- 预设位置 -->
            <GroupBox Header="预设位置" Style="{StaticResource AxisGroupStyle}">
                <StackPanel>
                    <ComboBox ItemsSource="{Binding PresetPositions}" 
                              SelectedItem="{Binding SelectedPreset}"
                              DisplayMemberPath="Name"
                              Margin="2"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Content="移动到" Margin="2"
                                Command="{Binding MoveToPresetCommand}"/>
                        <Button Grid.Column="1" Content="保存" Margin="2"
                                Command="{Binding SavePresetCommand}"/>
                    </Grid>
                </StackPanel>
            </GroupBox>
        </StackPanel>
    </ScrollViewer>
</UserControl>
