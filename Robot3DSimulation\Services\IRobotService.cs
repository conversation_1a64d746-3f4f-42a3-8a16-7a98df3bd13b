using System;
using System.Threading.Tasks;
using Robot3DSimulation.Models;

namespace Robot3DSimulation.Services
{
    /// <summary>
    /// 机器人服务接口，定义核心业务逻辑
    /// </summary>
    public interface IRobotService : IDisposable
    {
        /// <summary>
        /// 机器人模型
        /// </summary>
        RobotModel Robot { get; }

        /// <summary>
        /// 连接状态
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 是否正在运行数据更新
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 位置变化事件
        /// </summary>
        event EventHandler<RobotPositionChangedEventArgs> PositionChanged;

        /// <summary>
        /// 状态变化事件
        /// </summary>
        event EventHandler<RobotStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<bool> ConnectionStatusChanged;

        /// <summary>
        /// ARM状态变化事件
        /// </summary>
        event EventHandler<ArmStatusChangedEventArgs> ArmStatusChanged;

        /// <summary>
        /// 安全状态变化事件
        /// </summary>
        event EventHandler<SafetyStatusChangedEventArgs> SafetyStatusChanged;

        /// <summary>
        /// 轨迹记录事件
        /// </summary>
        event EventHandler<TrajectoryRecordedEventArgs> TrajectoryRecorded;

        /// <summary>
        /// 连接到机器人
        /// </summary>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync();

        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns>断开任务</returns>
        Task DisconnectAsync();

        /// <summary>
        /// 开始数据更新循环
        /// </summary>
        /// <returns>启动任务</returns>
        Task StartDataUpdateAsync();

        /// <summary>
        /// 停止数据更新循环
        /// </summary>
        /// <returns>停止任务</returns>
        Task StopDataUpdateAsync();

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="t">T轴目标位置</param>
        /// <param name="r">R轴目标位置</param>
        /// <param name="z">Z轴目标位置</param>
        /// <returns>移动是否成功</returns>
        Task<bool> MoveToPositionAsync(double t, double r, double z);

        /// <summary>
        /// 移动单个轴到指定位置
        /// </summary>
        /// <param name="axisName">轴名称（T、R、Z）</param>
        /// <param name="position">目标位置</param>
        /// <returns>移动是否成功</returns>
        Task<bool> MoveAxisAsync(string axisName, double position);

        /// <summary>
        /// 获取当前位置
        /// </summary>
        /// <returns>当前三轴位置</returns>
        Task<AxisPosition[]> GetCurrentPositionsAsync();

        /// <summary>
        /// 停止所有轴的移动
        /// </summary>
        /// <returns>停止任务</returns>
        Task<bool> StopAllAxesAsync();

        /// <summary>
        /// 设置紧急停止
        /// </summary>
        /// <param name="emergencyStop">是否紧急停止</param>
        /// <returns>设置任务</returns>
        Task<bool> SetEmergencyStopAsync(bool emergencyStop);

        /// <summary>
        /// 复位机器人
        /// </summary>
        /// <returns>复位是否成功</returns>
        Task<bool> ResetAsync();

        /// <summary>
        /// 回到原点位置
        /// </summary>
        /// <returns>归零是否成功</returns>
        Task<bool> HomeAsync();

        /// <summary>
        /// 检查位置是否安全
        /// </summary>
        /// <param name="t">T轴位置</param>
        /// <param name="r">R轴位置</param>
        /// <param name="z">Z轴位置</param>
        /// <returns>是否安全</returns>
        bool IsPositionSafe(double t, double r, double z);

        /// <summary>
        /// 获取机器人状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        Task<string> GetStatusInfoAsync();

        /// <summary>
        /// 设置运行模式
        /// </summary>
        /// <param name="autoMode">是否自动模式</param>
        /// <returns>设置任务</returns>
        Task<bool> SetRunModeAsync(bool autoMode);

        /// <summary>
        /// 更新ARM端状态
        /// </summary>
        /// <param name="endType">ARM端类型</param>
        /// <param name="status">状态</param>
        /// <param name="hasWafer">是否有晶圆</param>
        /// <param name="waferId">晶圆ID</param>
        /// <returns>更新任务</returns>
        Task UpdateArmEndStatusAsync(ArmEndType endType, ArmStatus status, bool hasWafer = false, string? waferId = null);

        // 兼容性方法
        Task<bool> MoveTAxisAsync(double position);
        Task<bool> MoveRAxisAsync(double position);
        Task<bool> MoveZAxisAsync(double position);
        Task<bool> SetArmStatusAsync(ArmEndType armType, ArmStatus status);
        (double T, double R, double Z) GetCurrentPosition();
        void ClearTrajectory();
    }
}
