using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Media.Media3D;
using System.Windows.Controls;
using Robot3DSimulation.Models;

namespace Robot3DSimulation.Services
{
    /// <summary>
    /// 3D渲染服务接口
    /// </summary>
    public interface I3DRenderService : IDisposable
    {
        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// 当前帧率
        /// </summary>
        double CurrentFrameRate { get; }

        /// <summary>
        /// 渲染质量级别
        /// </summary>
        int QualityLevel { get; set; }

        /// <summary>
        /// 渲染状态变化事件
        /// </summary>
        event EventHandler<string> RenderStatusChanged;

        /// <summary>
        /// 渲染统计更新事件
        /// </summary>
        event EventHandler<string> RenderStatsUpdated;

        /// <summary>
        /// 相机变化事件
        /// </summary>
        event EventHandler<string> CameraChanged;

        /// <summary>
        /// 场景初始化完成事件
        /// </summary>
        event EventHandler<bool> SceneInitialized;

        /// <summary>
        /// 初始化3D场景
        /// </summary>
        /// <param name="viewport">3D视口</param>
        /// <returns>初始化是否成功</returns>
        bool InitializeScene(Viewport3D viewport);

        /// <summary>
        /// 更新机器人位置
        /// </summary>
        /// <param name="tAxis">T轴位置</param>
        /// <param name="rAxis">R轴位置</param>
        /// <param name="zAxis">Z轴位置</param>
        void UpdateRobotPosition(AxisPosition tAxis, AxisPosition rAxis, AxisPosition zAxis);

        /// <summary>
        /// 显示轨迹
        /// </summary>
        /// <param name="trajectoryPoints">轨迹点列表</param>
        void ShowTrajectory(List<TrajectoryPoint> trajectoryPoints);

        /// <summary>
        /// 隐藏轨迹
        /// </summary>
        void HideTrajectory();

        /// <summary>
        /// 设置相机视图
        /// </summary>
        /// <param name="position">相机位置</param>
        /// <param name="lookDirection">观察方向</param>
        /// <param name="upDirection">向上方向</param>
        void SetCameraView(Point3D position, Vector3D lookDirection, Vector3D upDirection);

        /// <summary>
        /// 重置相机到默认位置
        /// </summary>
        void ResetCamera();

        /// <summary>
        /// 高亮显示指定轴
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="highlight">是否高亮</param>
        void HighlightAxis(string axisName, bool highlight);

        /// <summary>
        /// 设置ARM端状态显示
        /// </summary>
        /// <param name="endType">ARM端类型</param>
        /// <param name="status">状态</param>
        /// <param name="hasWafer">是否有晶圆</param>
        void SetArmEndStatus(ArmEndType endType, ArmStatus status, bool hasWafer);

        /// <summary>
        /// 显示/隐藏坐标轴
        /// </summary>
        /// <param name="show">是否显示</param>
        void ShowAxes(bool show);

        /// <summary>
        /// 显示/隐藏网格
        /// </summary>
        /// <param name="show">是否显示</param>
        void ShowGrid(bool show);

        /// <summary>
        /// 设置光照强度
        /// </summary>
        /// <param name="intensity">光照强度 (0.0 - 2.0)</param>
        void SetLightIntensity(double intensity);

        /// <summary>
        /// 设置背景颜色
        /// </summary>
        /// <param name="colorHex">十六进制颜色值</param>
        void SetBackgroundColor(string colorHex);

        /// <summary>
        /// 添加安全区域显示
        /// </summary>
        /// <param name="tMin">T轴最小值</param>
        /// <param name="tMax">T轴最大值</param>
        /// <param name="rMin">R轴最小值</param>
        /// <param name="rMax">R轴最大值</param>
        /// <param name="zMin">Z轴最小值</param>
        /// <param name="zMax">Z轴最大值</param>
        void ShowSafetyBounds(double tMin, double tMax, double rMin, double rMax, double zMin, double zMax);

        /// <summary>
        /// 隐藏安全区域
        /// </summary>
        void HideSafetyBounds();

        /// <summary>
        /// 开始动画
        /// </summary>
        /// <param name="fromT">起始T轴位置</param>
        /// <param name="fromR">起始R轴位置</param>
        /// <param name="fromZ">起始Z轴位置</param>
        /// <param name="toT">目标T轴位置</param>
        /// <param name="toR">目标R轴位置</param>
        /// <param name="toZ">目标Z轴位置</param>
        /// <param name="duration">动画时长（毫秒）</param>
        void StartAnimation(double fromT, double fromR, double fromZ, double toT, double toR, double toZ, int duration);

        /// <summary>
        /// 停止动画
        /// </summary>
        void StopAnimation();

        /// <summary>
        /// 截图
        /// </summary>
        /// <param name="filePath">保存路径</param>
        /// <param name="width">图片宽度</param>
        /// <param name="height">图片高度</param>
        /// <returns>截图是否成功</returns>
        bool TakeScreenshot(string filePath, int width = 1920, int height = 1080);

        /// <summary>
        /// 获取性能信息
        /// </summary>
        /// <returns>性能信息字符串</returns>
        string GetPerformanceInfo();

        /// <summary>
        /// 清理资源
        /// </summary>
        void Cleanup();

        // 异步兼容性方法
        Task<bool> InitializeSceneAsync(Viewport3D viewport);
        Task SetCameraAsync(Point3D position, Vector3D lookDirection, Vector3D upDirection);
        Task ClearTrajectoryAsync();
        Task StopAnimationAsync();
        Task StartAnimationAsync(double fromT, double fromR, double fromZ, double toT, double toR, double toZ, int duration);
        Task UpdateRobotPositionAsync(AxisPosition tAxis, AxisPosition rAxis, AxisPosition zAxis);
        Task SetRenderQualityAsync(int quality);
        Task<string> ExportSceneAsync(string filePath);
        Task AddTrajectoryPointAsync(TrajectoryPoint point);
        string GetRenderStats();
    }
}
