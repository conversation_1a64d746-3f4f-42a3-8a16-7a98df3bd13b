#nullable enable

using Calculator.Enums;

namespace Calculator.Services
{
    /// <summary>
    /// 计算器服务接口
    /// </summary>
    public interface ICalculatorService
    {
        /// <summary>
        /// 执行计算操作
        /// </summary>
        /// <param name="operand1">第一个操作数</param>
        /// <param name="operand2">第二个操作数</param>
        /// <param name="operation">操作类型</param>
        /// <returns>计算结果</returns>
        double Calculate(double operand1, double operand2, OperationType operation);

        /// <summary>
        /// 尝试解析数字字符串
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="result">解析结果</param>
        /// <returns>是否解析成功</returns>
        bool TryParseNumber(string input, out double result);

        /// <summary>
        /// 验证操作是否有效
        /// </summary>
        /// <param name="operand1">第一个操作数</param>
        /// <param name="operand2">第二个操作数</param>
        /// <param name="operation">操作类型</param>
        /// <returns>是否有效</returns>
        bool IsValidOperation(double operand1, double operand2, OperationType operation);
    }
}
