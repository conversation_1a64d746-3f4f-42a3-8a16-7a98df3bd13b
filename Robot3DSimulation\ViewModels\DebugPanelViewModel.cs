using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Robot3DSimulation.Models;
using Robot3DSimulation.Services;

namespace Robot3DSimulation.ViewModels
{
    /// <summary>
    /// 调试面板视图模型
    /// </summary>
    public partial class DebugPanelViewModel : ObservableObject
    {
        private readonly ILogger<DebugPanelViewModel> _logger;
        private readonly IRobotService _robotService;
        private readonly IModbusService _modbusService;
        private readonly I3DRenderService _renderService;
        private readonly StringBuilder _communicationLogBuilder;
        private readonly StringBuilder _errorLogBuilder;
        private Timer? _performanceTimer;
        private readonly PerformanceCounter? _cpuCounter;
        private bool _disposed;

        [ObservableProperty]
        private string _connectionStatus = "未连接";

        [ObservableProperty]
        private DateTime _connectionTime;

        [ObservableProperty]
        private double _communicationDelay;

        [ObservableProperty]
        private int _packetCount;

        [ObservableProperty]
        private double _tAxisPosition;

        [ObservableProperty]
        private double _rAxisPosition;

        [ObservableProperty]
        private double _zAxisPosition;

        [ObservableProperty]
        private double _updateFrequency;

        [ObservableProperty]
        private string _robotStatus = "未知";

        [ObservableProperty]
        private string _norseArmStatus = "未知";

        [ObservableProperty]
        private string _smoothArmStatus = "未知";

        [ObservableProperty]
        private string _safetyStatus = "未知";

        [ObservableProperty]
        private double _cpuUsage;

        [ObservableProperty]
        private long _memoryUsage;

        [ObservableProperty]
        private double _renderFps;

        [ObservableProperty]
        private int _triangleCount;

        [ObservableProperty]
        private string _communicationLog = string.Empty;

        [ObservableProperty]
        private string _errorLog = string.Empty;

        [ObservableProperty]
        private int _errorCount;

        [ObservableProperty]
        private string _logLevel = "全部";

        [ObservableProperty]
        private bool _showTimestamp = true;

        [ObservableProperty]
        private bool _autoScroll = true;

        [ObservableProperty]
        private string _monitorRegisterAddress = "40001";

        /// <summary>
        /// 寄存器监控列表
        /// </summary>
        public ObservableCollection<RegisterMonitorItem> RegisterMonitors { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="robotService">机器人服务</param>
        /// <param name="modbusService">Modbus服务</param>
        /// <param name="renderService">3D渲染服务</param>
        public DebugPanelViewModel(
            ILogger<DebugPanelViewModel> logger,
            IRobotService robotService,
            IModbusService modbusService,
            I3DRenderService renderService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _robotService = robotService ?? throw new ArgumentNullException(nameof(robotService));
            _modbusService = modbusService ?? throw new ArgumentNullException(nameof(modbusService));
            _renderService = renderService ?? throw new ArgumentNullException(nameof(renderService));

            _communicationLogBuilder = new StringBuilder();
            _errorLogBuilder = new StringBuilder();
            RegisterMonitors = new ObservableCollection<RegisterMonitorItem>();

            // 初始化性能计数器
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _cpuCounter.NextValue(); // 第一次调用返回0，需要预热
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法初始化CPU性能计数器");
            }

            // 订阅服务事件
            SubscribeToServiceEvents();

            // 启动性能监控
            StartPerformanceMonitoring();

            _logger.LogInformation("调试面板视图模型已初始化");
        }

        /// <summary>
        /// 清除日志命令
        /// </summary>
        [RelayCommand]
        private void ClearLog()
        {
            try
            {
                _communicationLogBuilder.Clear();
                CommunicationLog = string.Empty;
                _logger.LogInformation("通信日志已清除");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除日志时发生错误");
            }
        }

        /// <summary>
        /// 导出日志命令
        /// </summary>
        [RelayCommand]
        private async Task ExportLogAsync()
        {
            try
            {
                var fileName = $"CommunicationLog_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                await System.IO.File.WriteAllTextAsync(fileName, CommunicationLog);
                _logger.LogInformation("日志已导出到: {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出日志时发生错误");
            }
        }

        /// <summary>
        /// 清除错误命令
        /// </summary>
        [RelayCommand]
        private void ClearErrors()
        {
            try
            {
                _errorLogBuilder.Clear();
                ErrorLog = string.Empty;
                ErrorCount = 0;
                _logger.LogInformation("错误日志已清除");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除错误时发生错误");
            }
        }

        /// <summary>
        /// 添加寄存器监控命令
        /// </summary>
        [RelayCommand]
        private async Task AddRegisterMonitorAsync()
        {
            try
            {
                if (int.TryParse(MonitorRegisterAddress, out var address))
                {
                    var existingMonitor = RegisterMonitors.FirstOrDefault(m => m.Address == address);
                    if (existingMonitor == null)
                    {
                        var monitor = new RegisterMonitorItem
                        {
                            Address = address,
                            DataType = "Int16",
                            CurrentValue = 0,
                            LastUpdate = DateTime.Now
                        };

                        RegisterMonitors.Add(monitor);
                        _logger.LogInformation("已添加寄存器监控: {Address}", address);

                        // 立即读取一次值
                        await UpdateRegisterMonitorAsync(monitor);
                    }
                    else
                    {
                        _logger.LogWarning("寄存器 {Address} 已在监控列表中", address);
                    }
                }
                else
                {
                    _logger.LogWarning("无效的寄存器地址: {Address}", MonitorRegisterAddress);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加寄存器监控时发生错误");
            }
        }

        /// <summary>
        /// 清除寄存器监控命令
        /// </summary>
        [RelayCommand]
        private void ClearRegisterMonitor()
        {
            try
            {
                RegisterMonitors.Clear();
                _logger.LogInformation("寄存器监控已清除");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除寄存器监控时发生错误");
            }
        }

        /// <summary>
        /// 订阅服务事件
        /// </summary>
        private void SubscribeToServiceEvents()
        {
            try
            {
                // 机器人服务事件
                _robotService.ConnectionStatusChanged += OnRobotConnectionChanged;
                _robotService.StatusChanged += OnRobotStatusChanged;
                _robotService.PositionChanged += OnRobotPositionChanged;
                _robotService.ArmStatusChanged += OnArmStatusChanged;
                _robotService.SafetyStatusChanged += OnSafetyStatusChanged;

                // Modbus服务事件
                _modbusService.ConnectionStatusChanged += OnModbusConnectionChanged;
                _modbusService.CommunicationError += OnModbusCommunicationError;

                // 3D渲染服务事件
                _renderService.RenderStatsUpdated += OnRenderStatsUpdated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "订阅服务事件时发生错误");
            }
        }

        /// <summary>
        /// 启动性能监控
        /// </summary>
        private void StartPerformanceMonitoring()
        {
            try
            {
                _performanceTimer = new Timer(async _ => await UpdatePerformanceDataAsync(), 
                    null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动性能监控时发生错误");
            }
        }

        /// <summary>
        /// 更新性能数据
        /// </summary>
        private async Task UpdatePerformanceDataAsync()
        {
            try
            {
                // 更新CPU使用率
                if (_cpuCounter != null)
                {
                    CpuUsage = _cpuCounter.NextValue();
                }

                // 更新内存使用
                MemoryUsage = GC.GetTotalMemory(false) / 1024 / 1024; // MB

                // 更新渲染统计
                var renderStats = _renderService.GetRenderStats();
                // 解析统计文本，提取帧率和三角形数量
                try
                {
                    var lines = renderStats.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("帧率:"))
                        {
                            var fpsText = line.Split(':')[1].Replace("FPS", "").Trim();
                            if (double.TryParse(fpsText, out var fps))
                                RenderFps = fps;
                        }
                        else if (line.Contains("三角形数量:"))
                        {
                            var triangleText = line.Split(':')[1].Trim();
                            if (int.TryParse(triangleText, out var triangles))
                                TriangleCount = triangles;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析渲染统计文本时发生错误: {StatsText}", renderStats);
                }

                // 更新寄存器监控
                foreach (var monitor in RegisterMonitors.ToList())
                {
                    await UpdateRegisterMonitorAsync(monitor);
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新性能数据时发生错误");
            }
        }

        /// <summary>
        /// 更新寄存器监控
        /// </summary>
        private async Task UpdateRegisterMonitorAsync(RegisterMonitorItem monitor)
        {
            try
            {
                if (_modbusService.IsConnected)
                {
                    var value = await _modbusService.ReadHoldingRegisterAsync((ushort)monitor.Address);
                    monitor.CurrentValue = value;
                    monitor.LastUpdate = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新寄存器监控时发生错误: {Address}", monitor.Address);
            }
        }

        /// <summary>
        /// 添加通信日志
        /// </summary>
        private void AddCommunicationLog(string message, string level = "INFO")
        {
            try
            {
                var timestamp = ShowTimestamp ? $"[{DateTime.Now:HH:mm:ss.fff}] " : "";
                var logEntry = $"{timestamp}[{level}] {message}\n";

                _communicationLogBuilder.Append(logEntry);

                // 限制日志长度
                if (_communicationLogBuilder.Length > 100000) // 100KB
                {
                    var text = _communicationLogBuilder.ToString();
                    var halfLength = text.Length / 2;
                    var newlineIndex = text.IndexOf('\n', halfLength);
                    if (newlineIndex > 0)
                    {
                        _communicationLogBuilder.Clear();
                        _communicationLogBuilder.Append(text.Substring(newlineIndex + 1));
                    }
                }

                CommunicationLog = _communicationLogBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加通信日志时发生错误");
            }
        }

        /// <summary>
        /// 添加错误日志
        /// </summary>
        private void AddErrorLog(string message)
        {
            try
            {
                var timestamp = ShowTimestamp ? $"[{DateTime.Now:HH:mm:ss.fff}] " : "";
                var logEntry = $"{timestamp}[ERROR] {message}\n";

                _errorLogBuilder.Append(logEntry);
                ErrorCount++;

                // 限制错误日志长度
                if (_errorLogBuilder.Length > 50000) // 50KB
                {
                    var text = _errorLogBuilder.ToString();
                    var halfLength = text.Length / 2;
                    var newlineIndex = text.IndexOf('\n', halfLength);
                    if (newlineIndex > 0)
                    {
                        _errorLogBuilder.Clear();
                        _errorLogBuilder.Append(text.Substring(newlineIndex + 1));
                    }
                }

                ErrorLog = _errorLogBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加错误日志时发生错误");
            }
        }

        /// <summary>
        /// 机器人连接状态变化处理
        /// </summary>
        private void OnRobotConnectionChanged(object? sender, bool isConnected)
        {
            ConnectionStatus = isConnected ? "已连接" : "未连接";
            if (isConnected)
            {
                ConnectionTime = DateTime.Now;
                AddCommunicationLog("机器人连接成功");
            }
            else
            {
                AddCommunicationLog("机器人连接断开");
            }
        }

        /// <summary>
        /// 机器人状态变化处理
        /// </summary>
        private void OnRobotStatusChanged(object? sender, RobotStatusChangedEventArgs e)
        {
            RobotStatus = e.StatusMessage;
            AddCommunicationLog($"机器人状态: {e.StatusMessage}");

            if (e.IsError)
            {
                AddErrorLog($"机器人错误: {e.StatusMessage}");
            }
        }

        /// <summary>
        /// 机器人位置变化处理
        /// </summary>
        private void OnRobotPositionChanged(object? sender, RobotPositionChangedEventArgs e)
        {
            switch (e.AxisName)
            {
                case "T":
                    TAxisPosition = e.NewPosition;
                    break;
                case "R":
                    RAxisPosition = e.NewPosition;
                    break;
                case "Z":
                    ZAxisPosition = e.NewPosition;
                    break;
            }

            AddCommunicationLog($"{e.AxisName}轴位置: {e.NewPosition:F3}", "DEBUG");
        }

        /// <summary>
        /// ARM状态变化处理
        /// </summary>
        private void OnArmStatusChanged(object? sender, ArmStatusChangedEventArgs e)
        {
            var statusText = GetArmStatusText(e.Status);

            if (e.ArmType == ArmEndType.Norse)
            {
                NorseArmStatus = statusText;
            }
            else
            {
                SmoothArmStatus = statusText;
            }

            AddCommunicationLog($"{e.ArmType} ARM状态: {statusText}");
        }

        /// <summary>
        /// 安全状态变化处理
        /// </summary>
        private void OnSafetyStatusChanged(object? sender, SafetyStatusChangedEventArgs e)
        {
            SafetyStatus = e.IsSafe ? "安全" : "警告";
            AddCommunicationLog($"安全状态: {e.Message}", e.IsSafe ? "INFO" : "WARN");

            if (!e.IsSafe)
            {
                AddErrorLog($"安全警告: {e.Message}");
            }
        }

        /// <summary>
        /// Modbus连接状态变化处理
        /// </summary>
        private void OnModbusConnectionChanged(object? sender, bool isConnected)
        {
            AddCommunicationLog($"Modbus连接状态: {(isConnected ? "已连接" : "断开")}");
        }

        /// <summary>
        /// Modbus通信错误处理
        /// </summary>
        private void OnModbusCommunicationError(object? sender, string errorMessage)
        {
            AddCommunicationLog($"Modbus通信错误: {errorMessage}", "ERROR");
            AddErrorLog($"Modbus通信错误: {errorMessage}");
        }

        /// <summary>
        /// 渲染统计更新处理
        /// </summary>
        private void OnRenderStatsUpdated(object? sender, string statsText)
        {
            // 解析统计文本，提取帧率和三角形数量
            try
            {
                var lines = statsText.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains("帧率:"))
                    {
                        var fpsText = line.Split(':')[1].Replace("FPS", "").Trim();
                        if (double.TryParse(fpsText, out var fps))
                            RenderFps = fps;
                    }
                    else if (line.Contains("三角形数量:"))
                    {
                        var triangleText = line.Split(':')[1].Trim();
                        if (int.TryParse(triangleText, out var triangles))
                            TriangleCount = triangles;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析渲染统计文本时发生错误: {StatsText}", statsText);
            }
        }

        /// <summary>
        /// 获取ARM状态文本
        /// </summary>
        private static string GetArmStatusText(ArmStatus status)
        {
            return status switch
            {
                ArmStatus.Idle => "空闲",
                ArmStatus.Moving => "移动中",
                ArmStatus.Gripping => "夹取中",
                ArmStatus.Releasing => "释放中",
                ArmStatus.Error => "错误",
                ArmStatus.Maintenance => "维护中",
                _ => "未知"
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                // 停止性能监控
                _performanceTimer?.Dispose();

                // 释放性能计数器
                _cpuCounter?.Dispose();

                // 取消订阅事件
                if (_robotService != null)
                {
                    _robotService.ConnectionStatusChanged -= OnRobotConnectionChanged;
                    _robotService.StatusChanged -= OnRobotStatusChanged;
                    _robotService.PositionChanged -= OnRobotPositionChanged;
                    _robotService.ArmStatusChanged -= OnArmStatusChanged;
                    _robotService.SafetyStatusChanged -= OnSafetyStatusChanged;
                }

                if (_modbusService != null)
                {
                    _modbusService.ConnectionStatusChanged -= OnModbusConnectionChanged;
                    _modbusService.CommunicationError -= OnModbusCommunicationError;
                }

                if (_renderService != null)
                {
                    _renderService.RenderStatsUpdated -= OnRenderStatsUpdated;
                }

                _disposed = true;
                _logger.LogInformation("调试面板视图模型已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放调试面板视图模型时发生错误");
            }
        }
    }

    /// <summary>
    /// 寄存器监控项
    /// </summary>
    public partial class RegisterMonitorItem : ObservableObject
    {
        [ObservableProperty]
        private int _address;

        [ObservableProperty]
        private int _currentValue;

        [ObservableProperty]
        private string _dataType = "Int16";

        [ObservableProperty]
        private DateTime _lastUpdate;
    }
}
