using System;
using System.Threading.Tasks;
using Robot3DSimulation.Models;

namespace Robot3DSimulation.Services
{
    /// <summary>
    /// 配置管理服务接口
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 机器人配置
        /// </summary>
        RobotConfiguration RobotConfig { get; }

        /// <summary>
        /// Modbus配置
        /// </summary>
        ModbusConfiguration ModbusConfig { get; }

        /// <summary>
        /// 渲染配置
        /// </summary>
        RenderingConfiguration RenderingConfig { get; }

        /// <summary>
        /// 调试配置
        /// </summary>
        DebugConfiguration DebugConfig { get; }

        /// <summary>
        /// 配置变化事件
        /// </summary>
        event EventHandler<string> ConfigurationChanged;

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        /// <returns>加载是否成功</returns>
        Task<bool> LoadConfigurationAsync(string configFilePath = "appsettings.json");

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        /// <returns>保存是否成功</returns>
        Task<bool> SaveConfigurationAsync(string configFilePath = "appsettings.json");

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        void ResetToDefault();

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果和错误信息</returns>
        (bool IsValid, string ErrorMessage) ValidateConfiguration();

        /// <summary>
        /// 更新机器人配置
        /// </summary>
        /// <param name="config">新的机器人配置</param>
        void UpdateRobotConfiguration(RobotConfiguration config);

        /// <summary>
        /// 更新Modbus配置
        /// </summary>
        /// <param name="config">新的Modbus配置</param>
        void UpdateModbusConfiguration(ModbusConfiguration config);

        /// <summary>
        /// 更新渲染配置
        /// </summary>
        /// <param name="config">新的渲染配置</param>
        void UpdateRenderingConfiguration(RenderingConfiguration config);

        /// <summary>
        /// 更新调试配置
        /// </summary>
        /// <param name="config">新的调试配置</param>
        void UpdateDebugConfiguration(DebugConfiguration config);

        /// <summary>
        /// 导出配置到文件
        /// </summary>
        /// <param name="filePath">导出文件路径</param>
        /// <returns>导出是否成功</returns>
        Task<bool> ExportConfigurationAsync(string filePath);

        /// <summary>
        /// 从文件导入配置
        /// </summary>
        /// <param name="filePath">导入文件路径</param>
        /// <returns>导入是否成功</returns>
        Task<bool> ImportConfigurationAsync(string filePath);

        /// <summary>
        /// 获取配置摘要信息
        /// </summary>
        /// <returns>配置摘要</returns>
        string GetConfigurationSummary();

        /// <summary>
        /// 备份当前配置
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>备份是否成功</returns>
        Task<bool> BackupConfigurationAsync(string backupName);

        /// <summary>
        /// 恢复配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>恢复是否成功</returns>
        Task<bool> RestoreConfigurationAsync(string backupName);

        /// <summary>
        /// 获取可用的配置备份列表
        /// </summary>
        /// <returns>备份名称列表</returns>
        Task<string[]> GetAvailableBackupsAsync();
    }
}
