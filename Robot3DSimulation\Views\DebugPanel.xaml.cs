using System;
using System.Windows.Controls;
using Robot3DSimulation.ViewModels;

namespace Robot3DSimulation.Views
{
    /// <summary>
    /// DebugPanel.xaml 的交互逻辑
    /// </summary>
    public partial class DebugPanel : UserControl
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="viewModel">调试面板视图模型</param>
        public DebugPanel(DebugPanelViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        }
    }
}
