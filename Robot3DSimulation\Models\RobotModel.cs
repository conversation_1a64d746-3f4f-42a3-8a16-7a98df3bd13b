using System;
using System.Collections.Generic;
using CommunityToolkit.Mvvm.ComponentModel;

namespace Robot3DSimulation.Models
{
    /// <summary>
    /// 机器人主数据模型，包含三轴位置和ARM状态
    /// </summary>
    public partial class RobotModel : ObservableObject
    {
        /// <summary>
        /// T轴（平移轴）
        /// </summary>
        [ObservableProperty]
        private AxisPosition tAxis;

        /// <summary>
        /// R轴（旋转轴）
        /// </summary>
        [ObservableProperty]
        private AxisPosition rAxis;

        /// <summary>
        /// Z轴（垂直轴）
        /// </summary>
        [ObservableProperty]
        private AxisPosition zAxis;

        /// <summary>
        /// Norse端状态
        /// </summary>
        [ObservableProperty]
        private ArmEndInfo norseEnd;

        /// <summary>
        /// Smooth端状态
        /// </summary>
        [ObservableProperty]
        private ArmEndInfo smoothEnd;

        /// <summary>
        /// 连接状态
        /// </summary>
        [ObservableProperty]
        private bool isConnected;

        /// <summary>
        /// 系统状态信息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "未连接";

        /// <summary>
        /// 是否处于紧急停止状态
        /// </summary>
        [ObservableProperty]
        private bool isEmergencyStop;

        /// <summary>
        /// 是否正在自动运行
        /// </summary>
        [ObservableProperty]
        private bool isAutoMode;

        /// <summary>
        /// 最后通信时间
        /// </summary>
        [ObservableProperty]
        private DateTime lastCommunicationTime;

        /// <summary>
        /// 轨迹记录点
        /// </summary>
        [ObservableProperty]
        private List<TrajectoryPoint> trajectoryPoints;

        /// <summary>
        /// 是否显示轨迹
        /// </summary>
        [ObservableProperty]
        private bool showTrajectory;

        /// <summary>
        /// 构造函数
        /// </summary>
        public RobotModel()
        {
            // 初始化三轴
            TAxis = new AxisPosition("T轴", -1000, 1000, "mm", 50);
            RAxis = new AxisPosition("R轴", -360, 360, "度", 10);
            ZAxis = new AxisPosition("Z轴", 0, 500, "mm", 25);

            // 初始化ARM端
            NorseEnd = new ArmEndInfo
            {
                EndType = ArmEndType.Norse,
                Status = ArmStatus.Idle,
                LastUpdate = DateTime.Now
            };

            SmoothEnd = new ArmEndInfo
            {
                EndType = ArmEndType.Smooth,
                Status = ArmStatus.Idle,
                LastUpdate = DateTime.Now
            };

            // 初始化轨迹点列表
            TrajectoryPoints = new List<TrajectoryPoint>();
            
            LastCommunicationTime = DateTime.Now;
        }

        /// <summary>
        /// 获取所有轴的数组
        /// </summary>
        /// <returns>轴位置数组</returns>
        public AxisPosition[] GetAllAxes()
        {
            return new[] { TAxis, RAxis, ZAxis };
        }

        /// <summary>
        /// 检查所有轴是否在安全范围内
        /// </summary>
        /// <returns>是否安全</returns>
        public bool IsAllAxesSafe()
        {
            return TAxis.IsInSafeRange && RAxis.IsInSafeRange && ZAxis.IsInSafeRange;
        }

        /// <summary>
        /// 检查是否有轴正在移动
        /// </summary>
        /// <returns>是否有轴在移动</returns>
        public bool IsAnyAxisMoving()
        {
            return TAxis.IsMoving || RAxis.IsMoving || ZAxis.IsMoving;
        }

        /// <summary>
        /// 更新位置数据
        /// </summary>
        /// <param name="tPosition">T轴位置</param>
        /// <param name="rPosition">R轴位置</param>
        /// <param name="zPosition">Z轴位置</param>
        public void UpdatePositions(double tPosition, double rPosition, double zPosition)
        {
            TAxis.UpdatePosition(tPosition);
            RAxis.UpdatePosition(rPosition);
            ZAxis.UpdatePosition(zPosition);
            
            LastCommunicationTime = DateTime.Now;
            
            // 记录轨迹点
            if (ShowTrajectory)
            {
                AddTrajectoryPoint(tPosition, rPosition, zPosition);
            }
        }

        /// <summary>
        /// 添加轨迹点
        /// </summary>
        /// <param name="t">T轴位置</param>
        /// <param name="r">R轴位置</param>
        /// <param name="z">Z轴位置</param>
        public void AddTrajectoryPoint(double t, double r, double z)
        {
            var point = new TrajectoryPoint
            {
                T = t,
                R = r,
                Z = z,
                Timestamp = DateTime.Now
            };

            TrajectoryPoints.Add(point);

            // 限制轨迹点数量，避免内存过度使用
            const int maxPoints = 1000;
            if (TrajectoryPoints.Count > maxPoints)
            {
                TrajectoryPoints.RemoveAt(0);
            }
        }

        /// <summary>
        /// 清除轨迹
        /// </summary>
        public void ClearTrajectory()
        {
            TrajectoryPoints.Clear();
        }

        /// <summary>
        /// 设置紧急停止状态
        /// </summary>
        /// <param name="emergencyStop">是否紧急停止</param>
        public void SetEmergencyStop(bool emergencyStop)
        {
            IsEmergencyStop = emergencyStop;
            
            if (emergencyStop)
            {
                StatusMessage = "紧急停止";
                // 停止所有轴的移动
                TAxis.IsMoving = false;
                RAxis.IsMoving = false;
                ZAxis.IsMoving = false;
            }
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="connected">是否连接</param>
        /// <param name="message">状态消息</param>
        public void UpdateConnectionStatus(bool connected, string message = "")
        {
            IsConnected = connected;
            StatusMessage = string.IsNullOrEmpty(message) 
                ? (connected ? "已连接" : "未连接") 
                : message;
                
            if (connected)
            {
                LastCommunicationTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 获取系统整体状态描述
        /// </summary>
        /// <returns>状态描述</returns>
        public string GetOverallStatus()
        {
            if (IsEmergencyStop)
                return "紧急停止";

            if (!IsConnected)
                return "未连接";

            if (!IsAllAxesSafe())
                return "位置异常";

            if (IsAnyAxisMoving())
                return "运行中";

            return "就绪";
        }

        // 兼容性属性和方法
        public ArmEndInfo NorseArm => NorseEnd;
        public ArmEndInfo SmoothArm => SmoothEnd;

        public List<TrajectoryPoint> GetTrajectoryPoints() => TrajectoryPoints;

        public bool IsInSafeRange(double tPos, double rPos, double zPos)
        {
            return TAxis.IsPositionValid(tPos) &&
                   RAxis.IsPositionValid(rPos) &&
                   ZAxis.IsPositionValid(zPos);
        }

        public void SetTrajectoryBufferSize(int size)
        {
            // 实现轨迹缓冲区大小设置
            // 这里可以根据需要调整轨迹点的最大数量
        }
    }

    /// <summary>
    /// 轨迹点数据
    /// </summary>
    public class TrajectoryPoint
    {
        /// <summary>
        /// T轴位置
        /// </summary>
        public double T { get; set; }

        /// <summary>
        /// R轴位置
        /// </summary>
        public double R { get; set; }

        /// <summary>
        /// Z轴位置
        /// </summary>
        public double Z { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        // 兼容性属性
        public double TPosition => T;
        public double RAngle => R;
        public double ZHeight => Z;
    }
}
