using System;
using System.Windows.Controls;
using Robot3DSimulation.ViewModels;

namespace Robot3DSimulation.Views
{
    /// <summary>
    /// Robot3DView.xaml 的交互逻辑
    /// </summary>
    public partial class Robot3DView : UserControl
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="viewModel">3D视图模型</param>
        public Robot3DView(Robot3DViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
            
            // 设置视口引用给ViewModel
            if (viewModel is Robot3DViewModel vm)
            {
                vm.SetViewport(Viewport3D);
            }
        }
    }
}
