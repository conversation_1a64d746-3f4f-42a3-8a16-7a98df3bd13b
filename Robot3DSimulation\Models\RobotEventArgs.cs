using System;

namespace Robot3DSimulation.Models
{
    /// <summary>
    /// 机器人状态变化事件参数
    /// </summary>
    public class RobotStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage { get; }

        /// <summary>
        /// 状态代码
        /// </summary>
        public int StatusCode { get; }

        /// <summary>
        /// 是否为错误状态
        /// </summary>
        public bool IsError { get; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="statusMessage">状态消息</param>
        /// <param name="statusCode">状态代码</param>
        /// <param name="isError">是否为错误状态</param>
        /// <param name="isRunning">是否正在运行</param>
        public RobotStatusChangedEventArgs(string statusMessage, int statusCode = 0, bool isError = false, bool isRunning = false)
        {
            StatusMessage = statusMessage ?? throw new ArgumentNullException(nameof(statusMessage));
            StatusCode = statusCode;
            IsError = isError;
            IsRunning = isRunning;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 机器人位置变化事件参数
    /// </summary>
    public class RobotPositionChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 轴名称
        /// </summary>
        public string AxisName { get; }

        /// <summary>
        /// 新位置
        /// </summary>
        public double NewPosition { get; }

        /// <summary>
        /// 旧位置
        /// </summary>
        public double OldPosition { get; }

        /// <summary>
        /// 是否在安全范围内
        /// </summary>
        public bool IsInSafeRange { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="newPosition">新位置</param>
        /// <param name="oldPosition">旧位置</param>
        /// <param name="isInSafeRange">是否在安全范围内</param>
        public RobotPositionChangedEventArgs(string axisName, double newPosition, double oldPosition, bool isInSafeRange = true)
        {
            AxisName = axisName ?? throw new ArgumentNullException(nameof(axisName));
            NewPosition = newPosition;
            OldPosition = oldPosition;
            IsInSafeRange = isInSafeRange;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// ARM状态变化事件参数
    /// </summary>
    public class ArmStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// ARM端类型
        /// </summary>
        public ArmEndType ArmEndType { get; }

        /// <summary>
        /// ARM状态
        /// </summary>
        public ArmStatus Status { get; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string Description { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="armEndType">ARM端类型</param>
        /// <param name="status">ARM状态</param>
        /// <param name="description">状态描述</param>
        public ArmStatusChangedEventArgs(ArmEndType armEndType, ArmStatus status, string description = "")
        {
            ArmEndType = armEndType;
            Status = status;
            Description = description ?? string.Empty;
            Timestamp = DateTime.Now;
        }

        // 兼容性属性
        public ArmEndType ArmType => ArmEndType;
    }

    /// <summary>
    /// 轨迹记录事件参数
    /// </summary>
    public class TrajectoryRecordedEventArgs : EventArgs
    {
        /// <summary>
        /// 轨迹点
        /// </summary>
        public TrajectoryPoint Point { get; }

        /// <summary>
        /// 总轨迹点数
        /// </summary>
        public int TotalPoints { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="point">轨迹点</param>
        /// <param name="totalPoints">总轨迹点数</param>
        public TrajectoryRecordedEventArgs(TrajectoryPoint point, int totalPoints)
        {
            Point = point ?? throw new ArgumentNullException(nameof(point));
            TotalPoints = totalPoints;
        }

        // 兼容性属性
        public TrajectoryPoint TrajectoryPoint => Point;
    }

    /// <summary>
    /// 安全状态变化事件参数
    /// </summary>
    public class SafetyStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 轴名称
        /// </summary>
        public string AxisName { get; }

        /// <summary>
        /// 是否在安全范围内
        /// </summary>
        public bool IsInSafeRange { get; }

        /// <summary>
        /// 当前位置
        /// </summary>
        public double CurrentPosition { get; }

        /// <summary>
        /// 最小限制
        /// </summary>
        public double MinLimit { get; }

        /// <summary>
        /// 最大限制
        /// </summary>
        public double MaxLimit { get; }

        /// <summary>
        /// 安全边距
        /// </summary>
        public double SafetyMargin { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="isInSafeRange">是否在安全范围内</param>
        /// <param name="currentPosition">当前位置</param>
        /// <param name="minLimit">最小限制</param>
        /// <param name="maxLimit">最大限制</param>
        /// <param name="safetyMargin">安全边距</param>
        public SafetyStatusChangedEventArgs(string axisName, bool isInSafeRange,
            double currentPosition, double minLimit, double maxLimit, double safetyMargin)
        {
            AxisName = axisName ?? throw new ArgumentNullException(nameof(axisName));
            IsInSafeRange = isInSafeRange;
            CurrentPosition = currentPosition;
            MinLimit = minLimit;
            MaxLimit = maxLimit;
            SafetyMargin = safetyMargin;
            Timestamp = DateTime.Now;
        }

        // 兼容性属性
        public bool IsSafe => IsInSafeRange;
        public string Message => IsInSafeRange ? "位置安全" : $"位置超出安全范围: {CurrentPosition}";
    }

    /// <summary>
    /// 通信错误事件参数
    /// </summary>
    public class CommunicationErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="errorCode">错误代码</param>
        /// <param name="exception">异常对象</param>
        public CommunicationErrorEventArgs(string errorMessage, int errorCode = 0, Exception? exception = null)
        {
            ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
            ErrorCode = errorCode;
            Exception = exception;
            Timestamp = DateTime.Now;
        }
    }
}
