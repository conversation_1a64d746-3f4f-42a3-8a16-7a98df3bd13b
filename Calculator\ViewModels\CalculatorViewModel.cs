#nullable enable

using System;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Calculator.Services;
using Calculator.Models;
using Calculator.Enums;
using Calculator.Exceptions;

namespace Calculator.ViewModels
{
    /// <summary>
    /// 计算器视图模型，使用CommunityToolkit.Mvvm的Source Generators
    /// </summary>
    public partial class CalculatorViewModel : ObservableObject
    {
        private readonly ICalculatorService _calculatorService;
        private readonly CalculatorModel _model;

        /// <summary>
        /// 显示屏内容
        /// </summary>
        [ObservableProperty]
        private string display = "0";

        /// <summary>
        /// 当前输入的内容
        /// </summary>
        [ObservableProperty]
        private string currentInput = string.Empty;

        /// <summary>
        /// 是否显示错误状态
        /// </summary>
        [ObservableProperty]
        private bool hasError;

        /// <summary>
        /// 错误消息
        /// </summary>
        [ObservableProperty]
        private string? errorMessage;

        /// <summary>
        /// 初始化CalculatorViewModel
        /// </summary>
        /// <param name="calculatorService">计算器服务</param>
        public CalculatorViewModel(ICalculatorService calculatorService)
        {
            _calculatorService = calculatorService ?? throw new ArgumentNullException(nameof(calculatorService));
            _model = new CalculatorModel();
        }

        /// <summary>
        /// 数字按钮命令
        /// </summary>
        /// <param name="number">数字字符串</param>
        [RelayCommand]
        private void Number(string number)
        {
            try
            {
                ClearErrorIfNeeded();

                // 如果等待新操作数或显示为0，则重置输入
                if (_model.WaitingForOperand || Display == "0")
                {
                    CurrentInput = number;
                    _model.WaitingForOperand = false;
                }
                else
                {
                    CurrentInput += number;
                }

                Display = CurrentInput;
            }
            catch (Exception ex)
            {
                SetError($"输入错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 小数点按钮命令
        /// </summary>
        [RelayCommand]
        private void Decimal()
        {
            try
            {
                ClearErrorIfNeeded();

                if (_model.WaitingForOperand)
                {
                    CurrentInput = "0.";
                    _model.WaitingForOperand = false;
                }
                else if (!CurrentInput.Contains("."))
                {
                    CurrentInput += ".";
                }

                Display = CurrentInput;
            }
            catch (Exception ex)
            {
                SetError($"输入错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 操作按钮命令
        /// </summary>
        /// <param name="operation">操作符字符串</param>
        [RelayCommand]
        private void Operation(string operation)
        {
            try
            {
                ClearErrorIfNeeded();

                var operationType = ParseOperation(operation);
                if (operationType == null)
                {
                    SetError("无效的操作");
                    return;
                }

                // 解析当前输入
                if (!_calculatorService.TryParseNumber(string.IsNullOrEmpty(CurrentInput) ? Display : CurrentInput, out var currentValue))
                {
                    SetError("无效的数字");
                    return;
                }

                // 如果有待处理的操作，先执行它
                if (_model.IsOperationPending && !_model.WaitingForOperand)
                {
                    ExecutePendingOperation(currentValue);
                }
                else
                {
                    _model.FirstOperand = currentValue;
                }

                // 设置新操作
                _model.CurrentOperation = operationType;
                _model.IsOperationPending = true;
                _model.WaitingForOperand = true;
            }
            catch (Exception ex)
            {
                SetError($"操作错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 等号按钮命令
        /// </summary>
        [RelayCommand]
        private void Equals()
        {
            try
            {
                ClearErrorIfNeeded();

                if (!_model.IsOperationPending || _model.CurrentOperation == null)
                    return;

                // 解析第二个操作数
                if (!_calculatorService.TryParseNumber(string.IsNullOrEmpty(CurrentInput) ? Display : CurrentInput, out var secondOperand))
                {
                    SetError("无效的数字");
                    return;
                }

                // 执行计算
                var result = _calculatorService.Calculate(_model.FirstOperand, secondOperand, _model.CurrentOperation.Value);
                
                // 更新显示和状态
                Display = FormatResult(result);
                CurrentInput = Display;
                _model.Reset();
                _model.WaitingForOperand = true;
            }
            catch (CalculatorException ex)
            {
                SetError(ex.Message);
            }
            catch (Exception ex)
            {
                SetError($"计算错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除按钮命令
        /// </summary>
        [RelayCommand]
        private void Clear()
        {
            Display = "0";
            CurrentInput = string.Empty;
            _model.Reset();
            ClearError();
        }

        /// <summary>
        /// 退格按钮命令
        /// </summary>
        [RelayCommand]
        private void Backspace()
        {
            try
            {
                ClearErrorIfNeeded();

                if (_model.WaitingForOperand || CurrentInput.Length <= 1)
                {
                    CurrentInput = string.Empty;
                    Display = "0";
                }
                else
                {
                    CurrentInput = CurrentInput[..^1];
                    Display = CurrentInput;
                }
            }
            catch (Exception ex)
            {
                SetError($"退格错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行待处理的操作
        /// </summary>
        /// <param name="secondOperand">第二个操作数</param>
        private void ExecutePendingOperation(double secondOperand)
        {
            if (_model.CurrentOperation == null)
                return;

            var result = _calculatorService.Calculate(_model.FirstOperand, secondOperand, _model.CurrentOperation.Value);
            _model.FirstOperand = result;
            Display = FormatResult(result);
            CurrentInput = Display;
        }

        /// <summary>
        /// 解析操作字符串
        /// </summary>
        /// <param name="operation">操作字符串</param>
        /// <returns>操作类型</returns>
        private static OperationType? ParseOperation(string operation) => operation switch
        {
            "+" => OperationType.Addition,
            "-" => OperationType.Subtraction,
            "*" or "×" => OperationType.Multiplication,
            "/" or "÷" => OperationType.Division,
            _ => null
        };

        /// <summary>
        /// 格式化计算结果
        /// </summary>
        /// <param name="result">计算结果</param>
        /// <returns>格式化后的字符串</returns>
        private string FormatResult(double result)
        {
            if (double.IsInfinity(result))
                return "无穷大";
            if (double.IsNaN(result))
                return "错误";

            // 使用适当的精度显示结果
            return Math.Abs(result) < 1e-10 ? "0" : result.ToString("G15");
        }

        /// <summary>
        /// 设置错误状态
        /// </summary>
        /// <param name="message">错误消息</param>
        private void SetError(string message)
        {
            ErrorMessage = message;
            HasError = true;
            _model.SetError(message);
        }

        /// <summary>
        /// 清除错误状态
        /// </summary>
        private void ClearError()
        {
            ErrorMessage = null;
            HasError = false;
            _model.ClearError();
        }

        /// <summary>
        /// 如果需要则清除错误状态
        /// </summary>
        private void ClearErrorIfNeeded()
        {
            if (HasError)
            {
                ClearError();
            }
        }
    }
}
