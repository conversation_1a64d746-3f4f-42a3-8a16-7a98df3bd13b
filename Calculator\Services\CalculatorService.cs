#nullable enable

using System;
using System.Globalization;
using Calculator.Enums;
using Calculator.Exceptions;

namespace Calculator.Services
{
    /// <summary>
    /// 计算器服务实现，使用C# 8.0特性
    /// </summary>
    public class CalculatorService : ICalculatorService
    {
        /// <summary>
        /// 执行计算操作，使用C# 8.0 switch表达式
        /// </summary>
        /// <param name="operand1">第一个操作数</param>
        /// <param name="operand2">第二个操作数</param>
        /// <param name="operation">操作类型</param>
        /// <returns>计算结果</returns>
        /// <exception cref="CalculatorException">当操作无效时抛出</exception>
        public double Calculate(double operand1, double operand2, OperationType operation)
        {
            // 使用C# 8.0 switch表达式和when子句
            return operation switch
            {
                OperationType.Addition => operand1 + operand2,
                OperationType.Subtraction => operand1 - operand2,
                OperationType.Multiplication => operand1 * operand2,
                OperationType.Division when operand2 != 0 => operand1 / operand2,
                OperationType.Division => throw new CalculatorException("除数不能为零"),
                _ => throw new ArgumentException($"无效的操作类型: {operation}")
            };
        }

        /// <summary>
        /// 尝试解析数字字符串
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="result">解析结果</param>
        /// <returns>是否解析成功</returns>
        public bool TryParseNumber(string input, out double result)
        {
            result = 0;

            if (string.IsNullOrWhiteSpace(input))
                return false;

            // 使用InvariantCulture确保一致的数字解析
            return double.TryParse(input, NumberStyles.Float, CultureInfo.InvariantCulture, out result) &&
                   !double.IsInfinity(result) && !double.IsNaN(result);
        }

        /// <summary>
        /// 验证操作是否有效
        /// </summary>
        /// <param name="operand1">第一个操作数</param>
        /// <param name="operand2">第二个操作数</param>
        /// <param name="operation">操作类型</param>
        /// <returns>是否有效</returns>
        public bool IsValidOperation(double operand1, double operand2, OperationType operation)
        {
            // 使用C# 8.0 switch表达式进行验证
            return operation switch
            {
                OperationType.Addition => IsValidNumber(operand1) && IsValidNumber(operand2),
                OperationType.Subtraction => IsValidNumber(operand1) && IsValidNumber(operand2),
                OperationType.Multiplication => IsValidNumber(operand1) && IsValidNumber(operand2),
                OperationType.Division => IsValidNumber(operand1) && IsValidNumber(operand2) && operand2 != 0,
                _ => false
            };
        }

        /// <summary>
        /// 检查数字是否有效（不是无穷大或NaN）
        /// </summary>
        /// <param name="number">要检查的数字</param>
        /// <returns>是否有效</returns>
        private static bool IsValidNumber(double number)
        {
            return !double.IsInfinity(number) && !double.IsNaN(number);
        }

        /// <summary>
        /// 格式化计算结果
        /// </summary>
        /// <param name="result">计算结果</param>
        /// <returns>格式化后的字符串</returns>
        public string FormatResult(double result)
        {
            // 处理特殊值
            if (double.IsInfinity(result))
                return "无穷大";
            if (double.IsNaN(result))
                return "错误";

            // 使用适当的精度显示结果
            return Math.Abs(result) < 1e-10 ? "0" : result.ToString("G15", CultureInfo.InvariantCulture);
        }
    }
}
