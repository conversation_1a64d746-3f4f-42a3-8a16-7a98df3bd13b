using System;
using System.Threading.Tasks;
using Robot3DSimulation.Models;

namespace Robot3DSimulation.Services
{
    /// <summary>
    /// Modbus通信服务接口
    /// </summary>
    public interface IModbusService : IDisposable
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<bool> ConnectionStatusChanged;

        /// <summary>
        /// 通信错误事件
        /// </summary>
        event EventHandler<string> CommunicationError;

        /// <summary>
        /// 连接到Modbus设备
        /// </summary>
        /// <param name="config">Modbus配置</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync(ModbusConfiguration config);

        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns>断开任务</returns>
        Task DisconnectAsync();

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="count">寄存器数量</param>
        /// <returns>寄存器值数组</returns>
        Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count);

        /// <summary>
        /// 写入保持寄存器
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">要写入的值</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteHoldingRegistersAsync(ushort startAddress, ushort[] values);

        /// <summary>
        /// 读取单个保持寄存器
        /// </summary>
        /// <param name="address">寄存器地址</param>
        /// <returns>寄存器值</returns>
        Task<ushort> ReadHoldingRegisterAsync(ushort address);

        /// <summary>
        /// 写入单个保持寄存器
        /// </summary>
        /// <param name="address">寄存器地址</param>
        /// <param name="value">要写入的值</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteHoldingRegisterAsync(ushort address, ushort value);

        /// <summary>
        /// 读取32位浮点数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <returns>浮点数值</returns>
        Task<float> ReadFloat32Async(ushort startAddress);

        /// <summary>
        /// 写入32位浮点数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="value">要写入的浮点数</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteFloat32Async(ushort startAddress, float value);

        /// <summary>
        /// 读取32位整数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <returns>整数值</returns>
        Task<int> ReadInt32Async(ushort startAddress);

        /// <summary>
        /// 写入32位整数（占用2个寄存器）
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="value">要写入的整数</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteInt32Async(ushort startAddress, int value);

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <returns>连接是否正常</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// 获取连接信息
        /// </summary>
        /// <returns>连接信息字符串</returns>
        string GetConnectionInfo();
    }
}
