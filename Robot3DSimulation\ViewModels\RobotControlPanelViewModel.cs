using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Robot3DSimulation.Models;
using Robot3DSimulation.Services;

namespace Robot3DSimulation.ViewModels
{
    /// <summary>
    /// 机器人控制面板视图模型
    /// </summary>
    public partial class RobotControlPanelViewModel : ObservableObject
    {
        private readonly ILogger<RobotControlPanelViewModel> _logger;
        private readonly IRobotService _robotService;
        private readonly IConfigurationService _configService;
        private bool _disposed;

        [ObservableProperty]
        private bool _isConnected;

        [ObservableProperty]
        private bool _isRunning;

        [ObservableProperty]
        private string _connectionStatus = "未连接";

        [ObservableProperty]
        private string _robotStatus = "未知";

        // T轴控制
        [ObservableProperty]
        private double _tAxisPosition;

        [ObservableProperty]
        private double _tAxisTarget;

        [ObservableProperty]
        private double _tAxisMin;

        [ObservableProperty]
        private double _tAxisMax;

        [ObservableProperty]
        private bool _tAxisInSafeRange = true;

        // R轴控制
        [ObservableProperty]
        private double _rAxisPosition;

        [ObservableProperty]
        private double _rAxisTarget;

        [ObservableProperty]
        private double _rAxisMin;

        [ObservableProperty]
        private double _rAxisMax;

        [ObservableProperty]
        private bool _rAxisInSafeRange = true;

        // Z轴控制
        [ObservableProperty]
        private double _zAxisPosition;

        [ObservableProperty]
        private double _zAxisTarget;

        [ObservableProperty]
        private double _zAxisMin;

        [ObservableProperty]
        private double _zAxisMax;

        [ObservableProperty]
        private bool _zAxisInSafeRange = true;

        // ARM控制
        [ObservableProperty]
        private string _norseArmStatus = "空闲";

        [ObservableProperty]
        private string _smoothArmStatus = "空闲";

        [ObservableProperty]
        private bool _norseArmEnabled = true;

        [ObservableProperty]
        private bool _smoothArmEnabled = true;

        // 预设位置
        [ObservableProperty]
        private string _selectedPresetName = "";

        [ObservableProperty]
        private double _presetTPosition;

        [ObservableProperty]
        private double _presetRAngle;

        [ObservableProperty]
        private double _presetZHeight;

        /// <summary>
        /// 预设位置列表
        /// </summary>
        public ObservableCollection<PresetPosition> PresetPositions { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="robotService">机器人服务</param>
        /// <param name="configService">配置服务</param>
        public RobotControlPanelViewModel(
            ILogger<RobotControlPanelViewModel> logger,
            IRobotService robotService,
            IConfigurationService configService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _robotService = robotService ?? throw new ArgumentNullException(nameof(robotService));
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));

            PresetPositions = new ObservableCollection<PresetPosition>();

            // 订阅服务事件
            SubscribeToServiceEvents();

            // 初始化轴限制
            InitializeAxisLimits();

            // 初始化预设位置
            InitializePresetPositions();

            _logger.LogInformation("机器人控制面板视图模型已初始化");
        }

        /// <summary>
        /// 连接机器人命令
        /// </summary>
        [RelayCommand]
        private async Task ConnectAsync()
        {
            try
            {
                _logger.LogInformation("正在连接机器人...");
                var success = await _robotService.ConnectAsync();
                
                if (success)
                {
                    await _robotService.StartDataUpdateAsync();
                    _logger.LogInformation("机器人连接成功");
                }
                else
                {
                    _logger.LogError("机器人连接失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接机器人时发生错误");
            }
        }

        /// <summary>
        /// 断开连接命令
        /// </summary>
        [RelayCommand]
        private async Task DisconnectAsync()
        {
            try
            {
                _logger.LogInformation("正在断开机器人连接...");
                await _robotService.DisconnectAsync();
                _logger.LogInformation("机器人连接已断开");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开机器人连接时发生错误");
            }
        }

        /// <summary>
        /// 移动T轴命令
        /// </summary>
        [RelayCommand]
        private async Task MoveTAxisAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法移动T轴");
                    return;
                }

                var success = await _robotService.MoveTAxisAsync(TAxisTarget);
                if (success)
                {
                    _logger.LogInformation("T轴移动命令已发送，目标位置: {Position}", TAxisTarget);
                }
                else
                {
                    _logger.LogError("T轴移动命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动T轴时发生错误");
            }
        }

        /// <summary>
        /// 移动R轴命令
        /// </summary>
        [RelayCommand]
        private async Task MoveRAxisAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法移动R轴");
                    return;
                }

                var success = await _robotService.MoveRAxisAsync(RAxisTarget);
                if (success)
                {
                    _logger.LogInformation("R轴移动命令已发送，目标角度: {Angle}", RAxisTarget);
                }
                else
                {
                    _logger.LogError("R轴移动命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动R轴时发生错误");
            }
        }

        /// <summary>
        /// 移动Z轴命令
        /// </summary>
        [RelayCommand]
        private async Task MoveZAxisAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法移动Z轴");
                    return;
                }

                var success = await _robotService.MoveZAxisAsync(ZAxisTarget);
                if (success)
                {
                    _logger.LogInformation("Z轴移动命令已发送，目标高度: {Height}", ZAxisTarget);
                }
                else
                {
                    _logger.LogError("Z轴移动命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动Z轴时发生错误");
            }
        }

        /// <summary>
        /// 移动到位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveToPositionAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法移动到指定位置");
                    return;
                }

                var success = await _robotService.MoveToPositionAsync(TAxisTarget, RAxisTarget, ZAxisTarget);
                if (success)
                {
                    _logger.LogInformation("机器人移动命令已发送，目标位置: T={T}, R={R}, Z={Z}", 
                        TAxisTarget, RAxisTarget, ZAxisTarget);
                }
                else
                {
                    _logger.LogError("机器人移动命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动到指定位置时发生错误");
            }
        }

        /// <summary>
        /// 停止所有轴命令
        /// </summary>
        [RelayCommand]
        private async Task StopAllAxesAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法停止轴运动");
                    return;
                }

                var success = await _robotService.StopAllAxesAsync();
                if (success)
                {
                    _logger.LogInformation("所有轴停止命令已发送");
                }
                else
                {
                    _logger.LogError("停止轴运动命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止轴运动时发生错误");
            }
        }

        /// <summary>
        /// 紧急停止命令
        /// </summary>
        [RelayCommand]
        private async Task EmergencyStopAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法执行紧急停止");
                    return;
                }

                var success = await _robotService.SetEmergencyStopAsync(true);
                if (success)
                {
                    _logger.LogWarning("紧急停止命令已发送");
                }
                else
                {
                    _logger.LogError("紧急停止命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行紧急停止时发生错误");
            }
        }

        /// <summary>
        /// 复位命令
        /// </summary>
        [RelayCommand]
        private async Task ResetAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法复位");
                    return;
                }

                var success = await _robotService.ResetAsync();
                if (success)
                {
                    _logger.LogInformation("机器人复位命令已发送");
                }
                else
                {
                    _logger.LogError("机器人复位命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复位机器人时发生错误");
            }
        }

        /// <summary>
        /// 回到原点命令
        /// </summary>
        [RelayCommand]
        private async Task HomeAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法回到原点");
                    return;
                }

                var success = await _robotService.HomeAsync();
                if (success)
                {
                    _logger.LogInformation("机器人归零命令已发送");
                }
                else
                {
                    _logger.LogError("机器人归零命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "机器人归零时发生错误");
            }
        }

        /// <summary>
        /// Norse ARM夹取命令
        /// </summary>
        [RelayCommand]
        private async Task NorseGripAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法控制Norse ARM");
                    return;
                }

                var success = await _robotService.SetArmStatusAsync(ArmEndType.Norse, ArmStatus.Gripping);
                if (success)
                {
                    _logger.LogInformation("Norse ARM夹取命令已发送");
                }
                else
                {
                    _logger.LogError("Norse ARM夹取命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "控制Norse ARM夹取时发生错误");
            }
        }

        /// <summary>
        /// Norse ARM释放命令
        /// </summary>
        [RelayCommand]
        private async Task NorseReleaseAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法控制Norse ARM");
                    return;
                }

                var success = await _robotService.SetArmStatusAsync(ArmEndType.Norse, ArmStatus.Releasing);
                if (success)
                {
                    _logger.LogInformation("Norse ARM释放命令已发送");
                }
                else
                {
                    _logger.LogError("Norse ARM释放命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "控制Norse ARM释放时发生错误");
            }
        }

        /// <summary>
        /// Smooth ARM夹取命令
        /// </summary>
        [RelayCommand]
        private async Task SmoothGripAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法控制Smooth ARM");
                    return;
                }

                var success = await _robotService.SetArmStatusAsync(ArmEndType.Smooth, ArmStatus.Gripping);
                if (success)
                {
                    _logger.LogInformation("Smooth ARM夹取命令已发送");
                }
                else
                {
                    _logger.LogError("Smooth ARM夹取命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "控制Smooth ARM夹取时发生错误");
            }
        }

        /// <summary>
        /// Smooth ARM释放命令
        /// </summary>
        [RelayCommand]
        private async Task SmoothReleaseAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法控制Smooth ARM");
                    return;
                }

                var success = await _robotService.SetArmStatusAsync(ArmEndType.Smooth, ArmStatus.Releasing);
                if (success)
                {
                    _logger.LogInformation("Smooth ARM释放命令已发送");
                }
                else
                {
                    _logger.LogError("Smooth ARM释放命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "控制Smooth ARM释放时发生错误");
            }
        }

        /// <summary>
        /// 移动到预设位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveToPresetAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("机器人未连接，无法移动到预设位置");
                    return;
                }

                if (string.IsNullOrEmpty(SelectedPresetName))
                {
                    _logger.LogWarning("未选择预设位置");
                    return;
                }

                var preset = PresetPositions.FirstOrDefault(p => p.Name == SelectedPresetName);
                if (preset == null)
                {
                    _logger.LogWarning("找不到预设位置: {Name}", SelectedPresetName);
                    return;
                }

                var success = await _robotService.MoveToPositionAsync(preset.TPosition, preset.RAngle, preset.ZHeight);
                if (success)
                {
                    _logger.LogInformation("移动到预设位置 '{Name}' 命令已发送", preset.Name);
                }
                else
                {
                    _logger.LogError("移动到预设位置命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动到预设位置时发生错误");
            }
        }

        /// <summary>
        /// 保存预设位置命令
        /// </summary>
        [RelayCommand]
        private async Task SavePresetAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedPresetName))
                {
                    _logger.LogWarning("预设位置名称不能为空");
                    return;
                }

                var existingPreset = PresetPositions.FirstOrDefault(p => p.Name == SelectedPresetName);
                if (existingPreset != null)
                {
                    // 更新现有预设
                    existingPreset.TPosition = PresetTPosition;
                    existingPreset.RAngle = PresetRAngle;
                    existingPreset.ZHeight = PresetZHeight;
                    _logger.LogInformation("预设位置 '{Name}' 已更新", SelectedPresetName);
                }
                else
                {
                    // 创建新预设
                    var newPreset = new PresetPosition
                    {
                        Name = SelectedPresetName,
                        TPosition = PresetTPosition,
                        RAngle = PresetRAngle,
                        ZHeight = PresetZHeight
                    };
                    PresetPositions.Add(newPreset);
                    _logger.LogInformation("新预设位置 '{Name}' 已保存", SelectedPresetName);
                }

                // 保存到配置文件
                await SavePresetPositionsToConfigAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存预设位置时发生错误");
            }
        }

        /// <summary>
        /// 删除预设位置命令
        /// </summary>
        [RelayCommand]
        private async Task DeletePresetAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedPresetName))
                {
                    _logger.LogWarning("未选择要删除的预设位置");
                    return;
                }

                var preset = PresetPositions.FirstOrDefault(p => p.Name == SelectedPresetName);
                if (preset != null)
                {
                    PresetPositions.Remove(preset);
                    _logger.LogInformation("预设位置 '{Name}' 已删除", SelectedPresetName);

                    // 保存到配置文件
                    await SavePresetPositionsToConfigAsync();
                }
                else
                {
                    _logger.LogWarning("找不到要删除的预设位置: {Name}", SelectedPresetName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除预设位置时发生错误");
            }
        }

        /// <summary>
        /// 订阅服务事件
        /// </summary>
        private void SubscribeToServiceEvents()
        {
            try
            {
                _robotService.ConnectionStatusChanged += OnConnectionStatusChanged;
                _robotService.StatusChanged += OnRobotStatusChanged;
                _robotService.PositionChanged += OnPositionChanged;
                _robotService.ArmStatusChanged += OnArmStatusChanged;
                _robotService.SafetyStatusChanged += OnSafetyStatusChanged;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "订阅服务事件时发生错误");
            }
        }

        /// <summary>
        /// 初始化轴限制
        /// </summary>
        private void InitializeAxisLimits()
        {
            try
            {
                var robotConfig = _configService.RobotConfig;

                TAxisMin = robotConfig.TAxis.MinLimit;
                TAxisMax = robotConfig.TAxis.MaxLimit;
                RAxisMin = robotConfig.RAxis.MinLimit;
                RAxisMax = robotConfig.RAxis.MaxLimit;
                ZAxisMin = robotConfig.ZAxis.MinLimit;
                ZAxisMax = robotConfig.ZAxis.MaxLimit;

                _logger.LogInformation("轴限制已初始化");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化轴限制时发生错误");
            }
        }

        /// <summary>
        /// 初始化预设位置
        /// </summary>
        private void InitializePresetPositions()
        {
            try
            {
                // 添加默认预设位置
                PresetPositions.Add(new PresetPosition { Name = "原点", TPosition = 0, RAngle = 0, ZHeight = 0 });
                PresetPositions.Add(new PresetPosition { Name = "中心", TPosition = 250, RAngle = 0, ZHeight = 100 });
                PresetPositions.Add(new PresetPosition { Name = "最高点", TPosition = 250, RAngle = 0, ZHeight = 200 });

                _logger.LogInformation("预设位置已初始化");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化预设位置时发生错误");
            }
        }

        /// <summary>
        /// 保存预设位置到配置
        /// </summary>
        private async Task SavePresetPositionsToConfigAsync()
        {
            try
            {
                // 这里应该将预设位置保存到配置文件
                // 由于配置服务接口没有定义预设位置保存方法，这里只是记录日志
                _logger.LogInformation("预设位置已保存到配置");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存预设位置到配置时发生错误");
            }
        }

        /// <summary>
        /// 连接状态变化处理
        /// </summary>
        private void OnConnectionStatusChanged(object? sender, bool isConnected)
        {
            IsConnected = isConnected;
            ConnectionStatus = isConnected ? "已连接" : "未连接";
        }

        /// <summary>
        /// 机器人状态变化处理
        /// </summary>
        private void OnRobotStatusChanged(object? sender, RobotStatusChangedEventArgs e)
        {
            RobotStatus = e.StatusMessage;
            IsRunning = e.IsRunning;
        }

        /// <summary>
        /// 位置变化处理
        /// </summary>
        private void OnPositionChanged(object? sender, RobotPositionChangedEventArgs e)
        {
            switch (e.AxisName)
            {
                case "T":
                    TAxisPosition = e.NewPosition;
                    TAxisInSafeRange = e.IsInSafeRange;
                    break;
                case "R":
                    RAxisPosition = e.NewPosition;
                    RAxisInSafeRange = e.IsInSafeRange;
                    break;
                case "Z":
                    ZAxisPosition = e.NewPosition;
                    ZAxisInSafeRange = e.IsInSafeRange;
                    break;
            }
        }

        /// <summary>
        /// ARM状态变化处理
        /// </summary>
        private void OnArmStatusChanged(object? sender, ArmStatusChangedEventArgs e)
        {
            var statusText = GetArmStatusText(e.Status);

            if (e.ArmType == ArmEndType.Norse)
            {
                NorseArmStatus = statusText;
                NorseArmEnabled = e.Status != ArmStatus.Error;
            }
            else
            {
                SmoothArmStatus = statusText;
                SmoothArmEnabled = e.Status != ArmStatus.Error;
            }
        }

        /// <summary>
        /// 安全状态变化处理
        /// </summary>
        private void OnSafetyStatusChanged(object? sender, SafetyStatusChangedEventArgs e)
        {
            // 根据安全状态更新UI状态
            if (!e.IsSafe)
            {
                _logger.LogWarning("安全警告: {Message}", e.Message);
            }
        }

        /// <summary>
        /// 获取ARM状态文本
        /// </summary>
        private static string GetArmStatusText(ArmStatus status)
        {
            return status switch
            {
                ArmStatus.Idle => "空闲",
                ArmStatus.Moving => "移动中",
                ArmStatus.Gripping => "夹取中",
                ArmStatus.Releasing => "释放中",
                ArmStatus.Error => "错误",
                ArmStatus.Maintenance => "维护中",
                _ => "未知"
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                // 取消订阅事件
                if (_robotService != null)
                {
                    _robotService.ConnectionStatusChanged -= OnConnectionStatusChanged;
                    _robotService.StatusChanged -= OnRobotStatusChanged;
                    _robotService.PositionChanged -= OnPositionChanged;
                    _robotService.ArmStatusChanged -= OnArmStatusChanged;
                    _robotService.SafetyStatusChanged -= OnSafetyStatusChanged;
                }

                _disposed = true;
                _logger.LogInformation("机器人控制面板视图模型已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放机器人控制面板视图模型时发生错误");
            }
        }
    }

    /// <summary>
    /// 预设位置
    /// </summary>
    public partial class PresetPosition : ObservableObject
    {
        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private double _tPosition;

        [ObservableProperty]
        private double _rAngle;

        [ObservableProperty]
        private double _zHeight;
    }
}
